import { respErr } from "@/lib/resp";



export async function POST(req: Request) {
  try {
    const { url, fileName } = await req.json();

    if (!url) {
      return respErr("URL is required");
    }

    console.log(`[Download Proxy] Downloading: ${url}`);

    // 获取文件
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    // 获取内容类型
    const contentType = response.headers.get('content-type') || 'application/octet-stream';
    
    // 创建响应
    const headers = new Headers({
      'Content-Type': contentType,
      'Content-Disposition': `attachment; filename="${fileName || 'download'}"`,
      'Cache-Control': 'no-cache',
    });

    // 返回文件流
    return new Response(response.body, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('[Download Proxy] Error:', error);
    return respErr("Download failed");
  }
}
