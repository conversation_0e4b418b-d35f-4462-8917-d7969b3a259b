import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { createVolcengineProvider } from "@/services/volcengine-provider";



export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const taskId = searchParams.get('task_id');
    const modelType = searchParams.get('model_type') || 'fast';

    // 验证必需参数
    if (!taskId) {
      return respErr("Missing required parameter: task_id");
    }

    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "Authentication required");
    }

    // 创建火山引擎服务实例
    const volcengineProvider = createVolcengineProvider();

    let result;
    
    // 根据模型类型查询不同的ASR服务
    switch (modelType) {
      case 'bigmodel':
        result = await volcengineProvider.queryASRBigModel(taskId);
        break;
      case 'standard':
      case 'fast':
      default:
        result = await volcengineProvider.queryASRStandard(taskId);
        break;
    }

    if (result.code === 10000) {
      return respData({
        task_id: taskId,
        model_type: modelType,
        status: result.status,
        result: result.result
      });
    } else {
      return respErr(`Query failed: ${result.message}`);
    }

  } catch (error) {
    console.error('Volcengine ASR query error:', error);
    return respErr(`Query service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
