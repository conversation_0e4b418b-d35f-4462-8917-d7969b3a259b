import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { createVolcengineProvider } from "@/services/volcengine-provider";



export async function GET(req: Request) {
  try {
    const { searchParams } = new URL(req.url);
    const speakerId = searchParams.get('speaker_id');

    // 验证必需参数
    if (!speakerId) {
      return respErr("Missing required parameter: speaker_id");
    }

    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "Authentication required");
    }

    // 创建火山引擎服务实例
    const volcengineProvider = createVolcengineProvider();

    // 查询声音复刻状态
    const result = await volcengineProvider.queryVoiceCloneStatus(speakerId);

    if (result.BaseResp.StatusCode === 0) {
      // 状态映射
      const statusMap: Record<number, string> = {
        0: 'not_found',
        1: 'training',
        2: 'success',
        3: 'failed',
        4: 'active'
      };

      return respData({
        speaker_id: result.speaker_id,
        status: statusMap[result.status || 0] || 'unknown',
        create_time: result.create_time,
        version: result.version,
        demo_audio: result.demo_audio,
        ready_for_use: result.status === 2 || result.status === 4
      });
    } else {
      return respErr(`Status query failed: ${result.BaseResp.StatusMessage}`);
    }

  } catch (error) {
    console.error('Volcengine Voice Clone status query error:', error);
    return respErr(`Status query service error: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
