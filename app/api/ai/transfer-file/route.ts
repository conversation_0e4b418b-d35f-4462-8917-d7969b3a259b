import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { transferGRSAIFile, extractUrlsFromGRSAIResponse, transferMultipleFiles } from "@/services/file-transfer";
import { getAIModelUsageByRequestId, updateAIModelUsage } from "@/models/ai-model";



export async function POST(req: Request) {
  try {
    const { url, urls, request_id, model_type } = await req.json();

    // 验证用户身份
    // const user_uuid = await getUserUuid();
    // if (!user_uuid) {
    //   return respJson(-2, "Authentication required");
    // }

    console.log(`[Transfer API] Request - url: ${url}, urls: ${urls}, request_id: ${request_id}, model_type: ${model_type}`);

    let transferResults;

    if (url) {
      // 单个文件转存
      console.log(`[Transfer API] Transferring single file: ${url}`);
      const result = await transferGRSAIFile(url, model_type || 'image');
      transferResults = [{ originalUrl: url, ...result }];
    } else if (urls && Array.isArray(urls)) {
      // 批量文件转存
      console.log(`[Transfer API] Transferring multiple files:`, urls);
      transferResults = await transferMultipleFiles(urls, model_type || 'image');
    } else if (request_id) {
      // 从数据库记录中获取文件并转存
      console.log(`[Transfer API] Transferring files from request: ${request_id}`);
      
      const usage = await getAIModelUsageByRequestId(request_id);
      if (!usage) {
        return respErr("Request not found");
      }

      // // 验证用户权限
      // if (usage.user_uuid !== user_uuid) {
      //   return respErr("Access denied");
      // }

      if (!usage.response_data) {
        return respErr("No response data found");
      }

      // 提取所有需要转存的URL
      const urlsToTransfer = extractUrlsFromGRSAIResponse(usage.response_data);
      console.log(`[Transfer API] Extracted URLs from response:`, urlsToTransfer);

      if (urlsToTransfer.length === 0) {
        return respErr("No URLs found in response data");
      }

      // 推断模型类型
      const inferredModelType = usage.model_id?.includes('veo') ? 'video' : 'image';
      
      // 批量转存，使用request_id作为任务ID
      transferResults = await transferMultipleFiles(urlsToTransfer, inferredModelType, request_id);

      // 如果转存成功，更新数据库记录
      const successfulTransfers = transferResults.filter(r => r.success);
      if (successfulTransfers.length > 0) {
        // 创建URL映射
        const urlMapping: Record<string, string> = {};
        successfulTransfers.forEach(result => {
          if (result.url) {
            urlMapping[result.originalUrl] = result.url;
          }
        });

        // 更新响应数据中的URL
        const updatedResponseData = JSON.parse(JSON.stringify(usage.response_data));
        
        // 更新主URL
        if (updatedResponseData.url && urlMapping[updatedResponseData.url]) {
          updatedResponseData.url = urlMapping[updatedResponseData.url];
        }
        
        // 更新results数组中的URL
        if (updatedResponseData.results && Array.isArray(updatedResponseData.results)) {
          updatedResponseData.results.forEach((result: any) => {
            if (result.url && urlMapping[result.url]) {
              result.url = urlMapping[result.url];
            }
          });
        }

        // 更新数据库记录
        await updateAIModelUsage(request_id, {
          response_data: updatedResponseData
        });

        console.log(`[Transfer API] Updated database record with new URLs`);
      }
    } else {
      return respErr("Missing required parameter: url, urls, or request_id");
    }

    // 统计转存结果
    const successCount = transferResults.filter(r => r.success).length;
    const failCount = transferResults.length - successCount;

    console.log(`[Transfer API] Transfer completed - Success: ${successCount}, Failed: ${failCount}`);

    return respData({
      results: transferResults,
      summary: {
        total: transferResults.length,
        success: successCount,
        failed: failCount
      }
    });
  } catch (error) {
    console.error("[Transfer API] Request processing failed:", error);
    return respErr("File transfer failed");
  }
}
