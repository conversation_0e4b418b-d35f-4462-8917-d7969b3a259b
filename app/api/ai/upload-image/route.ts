import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { newStorage } from "@/lib/storage";
import { NextRequest } from "next/server";



export async function POST(req: NextRequest) {
  try {
    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    console.log(`[Image Upload] User ${user_uuid} uploading image`);

    const formData = await req.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return respErr("No file provided");
    }

    console.log(`[Image Upload] File info: ${file.name}, size: ${file.size}, type: ${file.type}`);

    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      return respErr("Only image files are allowed");
    }

    // 验证文件大小 (最大 10MB)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return respErr("File size too large. Maximum 10MB allowed");
    }

    // 生成唯一文件名
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `temp/ai-uploads/${user_uuid}/${timestamp}-${randomString}.${fileExtension}`;

    try {
      // 将文件转换为Buffer
      const arrayBuffer = await file.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      // 上传到R2存储
      const storage = newStorage();
      const uploadResult = await storage.uploadFile({
        body: buffer,
        key: fileName,
        contentType: file.type,
        disposition: 'inline'
      });

      console.log(`[Image Upload] Upload successful:`, uploadResult);

      return respData({
        url: uploadResult.url,
        filename: uploadResult.filename,
        key: uploadResult.key,
        size: file.size,
        type: file.type
      });
    } catch (uploadError) {
      console.error("Upload failed:", uploadError);
      return respErr("Failed to upload image");
    }
  } catch (error) {
    console.error("Image upload error:", error);
    return respErr("Image upload failed");
  }
}
