import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { GRSAIProvider } from "@/services/grsai-provider";
import { getAIModelUsageByRequestId, updateAIModelUsage } from "@/models/ai-model";
import { getIsoTimestr } from "@/lib/time";
import { extractUrlsFromGRSAIResponse, transferMultipleFiles, updateResponseDataUrls } from "@/services/file-transfer";
import { decreaseCreditsForAIModel, refundCreditsForAIModel } from "@/services/credit";



export async function POST(req: Request) {
  try {
    const { request_id, task_id } = await req.json();
    console.log(`[Result API] Received request - request_id: ${request_id}, task_id: ${task_id}`);

    if (!request_id && !task_id) {
      return respErr("Missing required parameter: request_id or task_id");
    }

    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "Authentication required");
    }

    let usage;
    let grsaiTaskId = task_id;

    if (request_id) {
      console.log(`[Result API] Looking up usage record for request_id: ${request_id}`);
      // 通过request_id查找使用记录
      usage = await getAIModelUsageByRequestId(request_id);
      if (!usage) {
        console.log(`[Result API] No usage record found for request_id: ${request_id}`);
        return respErr("Request not found");
      }

      console.log(`[Result API] Found usage record:`, usage);

      // 验证用户权限
      if (usage.user_uuid !== user_uuid) {
        return respErr("Access denied");
      }

      // 从响应数据中获取task_id
      if (usage.response_data && usage.response_data.data && usage.response_data.data.id) {
        grsaiTaskId = usage.response_data.data.id;
        console.log(`[Result API] Extracted GRSAI task ID from usage: ${grsaiTaskId}`);
      } else if (usage.response_data && usage.response_data.id) {
        grsaiTaskId = usage.response_data.id;
        console.log(`[Result API] Extracted GRSAI task ID from usage (direct): ${grsaiTaskId}`);
      }
    }

    // 如果没有找到grsaiTaskId，但有request_id，尝试直接使用request_id作为任务ID
    if (!grsaiTaskId && request_id) {
      console.log(`[Result API] Using request_id as task_id: ${request_id}`);
      grsaiTaskId = request_id;
    }

    if (!grsaiTaskId) {
      console.log(`[Result API] No task ID available`);
      return respErr("Task ID not found");
    }

    try {
      // 查询GRSAI结果
      console.log(`[Result API] Querying GRSAI result for task ID: ${grsaiTaskId}`);
      const grsaiProvider = new GRSAIProvider();
      const result = await grsaiProvider.getResult(grsaiTaskId);

      console.log(`[Result API] GRSAI result response:`, result);

      if (result.code !== 0) {
        console.log(`[Result API] GRSAI API error: code=${result.code}, msg=${result.msg}`);
        return respErr(`GRSAI API error: ${result.msg}`);
      }

      const taskData = result.data;
      console.log(`[Result API] Task data:`, taskData);

      // 如果没有找到使用记录但有task_id，直接处理GRSAI响应
      if (!usage && task_id) {
        console.log(`[Result API] Direct task query without usage record`);
        const directModelType = getTypeFromModel(task_id);
        const response = {
          id: task_id,
          type: directModelType,
          status: mapGRSAIStatus(taskData.status),
          progress: taskData.progress,
          result: buildResult(taskData, directModelType),
          error: taskData.status === 'failed' ? {
            reason: taskData.failure_reason || 'error',
            detail: taskData.error || 'Unknown error'
          } : undefined
        };
        return respData(response);
      }
      
      // 构建统一响应格式
      const modelType = usage ? getTypeFromModel(usage.model_id) : getTypeFromModel(grsaiTaskId);
      const response = {
        id: request_id || grsaiTaskId,
        type: modelType,
        status: mapGRSAIStatus(taskData.status),
        progress: taskData.progress,
        result: buildResult(taskData, modelType),
        error: taskData.status === 'failed' ? {
          reason: taskData.failure_reason || 'error',
          detail: taskData.error || 'Unknown error'
        } : undefined
      };

      // 如果任务成功完成，进行积分扣减和文件转存
      if (response.status === 'success' && taskData.url) {
        console.log(`[Result API] Task completed successfully, starting credit deduction and file transfer`);

        // 扣减积分（如果还没有扣减过）
        if (usage && usage.status !== 'success') {
          try {
            await decreaseCreditsForAIModel({
              user_uuid,
              model_id: usage.model_id,
              request_id: request_id,
              credits: usage.credits_consumed
            });
            console.log(`[Result API] Credits deducted: ${usage.credits_consumed} for request ${request_id}`);
          } catch (creditError) {
            console.error(`[Result API] Failed to deduct credits:`, creditError);
            // 积分扣减失败不影响主流程，但要记录错误
          }
        }

        try {
          // 提取所有需要转存的URL
          const urlsToTransfer = extractUrlsFromGRSAIResponse(taskData);
          console.log(`[Result API] URLs to transfer:`, urlsToTransfer);

          if (urlsToTransfer.length > 0) {
            // 推断模型类型
            const inferredModelType = usage?.model_id?.includes('veo') ? 'video' : 'image';

            // 批量转存文件，使用request_id作为任务ID
            const transferResults = await transferMultipleFiles(urlsToTransfer, inferredModelType, request_id);
            console.log(`[Result API] Transfer results:`, transferResults);

            // 检查是否有成功的转存
            const successfulTransfers = transferResults.filter(r => r.success);
            if (successfulTransfers.length > 0) {
              // 创建URL映射
              const urlMapping: Record<string, string> = {};
              successfulTransfers.forEach(result => {
                if (result.url) {
                  urlMapping[result.originalUrl] = result.url;
                }
              });

              // 更新响应数据中的URL
              const updatedTaskData = updateResponseDataUrls(taskData, urlMapping);
              console.log(`[Result API] Updated URLs in response data`);

              // 更新response中的结果
              response.result = buildResult(updatedTaskData, modelType);

              // 更新数据库记录
              if (usage) {
                await updateAIModelUsage(request_id, {
                  status: 'success',
                  response_data: { ...usage.response_data, ...updatedTaskData },
                  completed_at: getIsoTimestr()
                });
              }
            } else {
              console.log(`[Result API] File transfer failed, using original URLs`);
              // 即使转存失败，也要更新状态
              if (usage) {
                await updateAIModelUsage(request_id, {
                  status: 'success',
                  response_data: { ...usage.response_data, ...taskData },
                  completed_at: getIsoTimestr()
                });
              }
            }
          }
        } catch (transferError) {
          console.error(`[Result API] File transfer error:`, transferError);
          // 转存失败不影响主流程，继续使用原始URL
          if (usage) {
            await updateAIModelUsage(request_id, {
              status: 'success',
              response_data: { ...usage.response_data, ...taskData },
              completed_at: getIsoTimestr()
            });
          }
        }
      } else if (usage && usage.status !== response.status) {
        // 其他状态变化的更新

        // 如果任务失败且错误类型为"error"，退还积分
        if (response.status === 'failed' && response.error?.reason === 'error' && usage.status !== 'failed') {
          try {
            await refundCreditsForAIModel({
              user_uuid,
              model_id: usage.model_id,
              request_id: request_id,
              credits: usage.credits_consumed,
              original_trans_no: `${request_id}_deduction` // 简化的交易号
            });
            console.log(`[Result API] Credits refunded: ${usage.credits_consumed} for failed request ${request_id}`);
          } catch (refundError) {
            console.error(`[Result API] Failed to refund credits:`, refundError);
          }
        }

        await updateAIModelUsage(request_id, {
          status: response.status === 'success' ? 'success' :
                  response.status === 'failed' ? 'failed' : 'pending',
          response_data: { ...usage.response_data, ...taskData },
          completed_at: response.status === 'success' || response.status === 'failed'
            ? getIsoTimestr() : undefined
        });
      }

      return respData(response);
    } catch (error) {
      console.error("Failed to query GRSAI result:", error);
      return respErr("Failed to query result");
    }
  } catch (error) {
    console.error("Request processing failed:", error);
    return respErr("Request processing failed");
  }
}

/**
 * 根据模型ID或任务ID推断类型
 */
function getTypeFromModel(modelId: string): string {
  if (modelId.includes('veo')) return 'video';
  if (modelId.includes('flux') || modelId.includes('image') || modelId.includes('sora')) return 'image';
  // 对于UUID格式的任务ID，默认返回image（因为大多数异步任务是图像生成）
  if (modelId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
    return 'image';
  }
  return 'text';
}

/**
 * 映射GRSAI状态到统一状态
 */
function mapGRSAIStatus(status: string): string {
  switch (status) {
    case 'succeeded': return 'success';
    case 'failed': return 'failed';
    case 'running': return 'running';
    default: return 'pending';
  }
}

/**
 * 根据URL扩展名判断文件类型
 */
function getFileTypeFromUrl(url: string): 'image' | 'video' | 'unknown' {
  const imageExtensions = ['png', 'jpg', 'jpeg', 'webp', 'gif', 'bmp', 'svg', 'tiff', 'ico'];
  const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv', 'm4v', '3gp'];

  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname.toLowerCase();
    const extension = pathname.split('.').pop();

    if (extension && imageExtensions.includes(extension)) {
      return 'image';
    }
    if (extension && videoExtensions.includes(extension)) {
      return 'video';
    }
  } catch (e) {
    // URL解析失败，返回unknown
  }

  return 'unknown';
}

/**
 * 构建结果对象
 */
function buildResult(taskData: any, modelType: string): any {
  if (taskData.status !== 'succeeded') {
    return undefined;
  }

  if (!taskData.url) {
    return undefined;
  }

  // 首先根据模型类型判断，然后用文件扩展名作为辅助判断
  const fileType = getFileTypeFromUrl(taskData.url);
  let isVideo = false;

  if (modelType === 'video') {
    isVideo = true;
  } else if (modelType === 'image' || modelType === 'multimodal') {
    isVideo = false;
  } else {
    // 如果模型类型不明确，使用文件扩展名判断
    isVideo = fileType === 'video';
  }

  if (isVideo) {
    return {
      video: {
        url: taskData.url
      }
    };
  } else {
    // 图像结果
    const images = [];
    const addedUrls = new Set<string>(); // 用于去重

    // 处理单个图像
    if (taskData.url && !addedUrls.has(taskData.url)) {
      images.push({
        url: taskData.url,
        width: taskData.width || 1024,
        height: taskData.height || 1024
      });
      addedUrls.add(taskData.url);
    }

    // 处理多个结果（如果有results数组）
    if (taskData.results && Array.isArray(taskData.results)) {
      taskData.results.forEach((result: any) => {
        if (result.url && !addedUrls.has(result.url)) {
          images.push({
            url: result.url,
            width: result.width || 1024,
            height: result.height || 1024
          });
          addedUrls.add(result.url);
        }
      });
    }

    return { images };
  }
}
