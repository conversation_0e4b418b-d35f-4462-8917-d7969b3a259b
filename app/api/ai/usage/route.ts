import { respData, respErr, respJson } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getUserAIModelUsage, getUserCreditsUsageStats } from "@/models/ai-model";
import { getCreditsByUserUuid } from "@/models/credit";



export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '20');
    const type = url.searchParams.get('type'); // 'usage' | 'credits' | 'stats'

    // 验证用户身份
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respJson(-2, "Authentication required");
    }

    switch (type) {
      case 'usage':
        // 获取AI模型使用记录
        const usageRecords = await getUserAIModelUsage(user_uuid, page, limit);
        return respData({
          records: usageRecords,
          page,
          limit,
          total: usageRecords.length
        });

      case 'credits':
        // 获取积分交易记录
        const creditRecords = await getCreditsByUserUuid(user_uuid, page, limit);
        return respData({
          records: creditRecords,
          page,
          limit,
          total: creditRecords?.length || 0
        });

      case 'stats':
        // 获取使用统计
        const stats = await getUserCreditsUsageStats(user_uuid);
        return respData({
          stats,
          summary: calculateSummary(stats)
        });

      default:
        // 默认返回综合信息
        const [usage, credits, statistics] = await Promise.all([
          getUserAIModelUsage(user_uuid, 1, 10),
          getCreditsByUserUuid(user_uuid, 1, 10),
          getUserCreditsUsageStats(user_uuid)
        ]);

        return respData({
          recent_usage: usage,
          recent_credits: credits,
          stats: statistics,
          summary: calculateSummary(statistics)
        });
    }
  } catch (error) {
    console.error("Failed to fetch usage data:", error);
    return respErr("Failed to fetch usage data");
  }
}

/**
 * 计算使用统计摘要
 */
function calculateSummary(stats: any[]): any {
  if (!stats || stats.length === 0) {
    return {
      total_usage_count: 0,
      total_credits_consumed: 0,
      most_used_model: null,
      success_rate: 0,
      model_breakdown: []
    };
  }

  const totalUsage = stats.reduce((sum, stat) => sum + (stat.usage_count || 0), 0);
  const totalCredits = stats.reduce((sum, stat) => sum + (stat.total_credits_consumed || 0), 0);
  const totalSuccess = stats.reduce((sum, stat) => sum + (stat.success_count || 0), 0);

  const mostUsedModel = stats.reduce((prev, current) =>
    (prev.usage_count || 0) > (current.usage_count || 0) ? prev : current
  );

  const successRate = totalUsage > 0 ? (totalSuccess / totalUsage * 100) : 0;

  return {
    total_usage_count: totalUsage,
    total_credits_consumed: totalCredits,
    most_used_model: mostUsedModel && mostUsedModel.model_id ? {
      model_id: mostUsedModel.model_id,
      model_name: mostUsedModel.model_name,
      usage_count: mostUsedModel.usage_count
    } : null,
    success_rate: Math.round(successRate * 100) / 100,
    model_breakdown: stats.map(stat => ({
      model_name: stat.model_name || '',
      model_type: stat.model_type || '',
      usage_count: stat.usage_count || 0,
      credits_consumed: stat.total_credits_consumed || 0
    }))
  };
}
