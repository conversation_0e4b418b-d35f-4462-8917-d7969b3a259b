"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";


export default function TestReplicatePage() {
  const [prompt, setPrompt] = useState("a korean woman pale skin, only wearing lace underware, front side camera");
  const [apiKey, setApiKey] = useState("");
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);

  const handleTest = async () => {
    if (!prompt.trim()) {
      toast.error("请输入提示词");
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      // 直接调用 Replicate provider 测试接口
      const response = await fetch('/api/test/replicate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt })
      });

      const data = await response.json();

      if (data.code === 0) {
        setResult(data.data);
        toast.success("测试成功！");
      } else {
        toast.error(data.msg || "测试失败");
      }
    } catch (error) {
      console.error('Test failed:', error);
      toast.error("网络错误");
    } finally {
      setLoading(false);
    }
  };

  const handleTestUnified = async () => {
    if (!prompt.trim()) {
      toast.error("请输入提示词");
      return;
    }

    if (!apiKey.trim()) {
      toast.error("请输入 API Key");
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      // 通过统一 AI API 调用
      const response = await fetch('/api/ai/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify({
          model: 'black-forest-labs/flux-krea-dev',
          type: 'image',
          prompt,
          options: {
            output_quality: 90,
            output_format: 'webp',
            disable_safety_checker:true
          }
        })
      });

      const data = await response.json();

      if (data.code === 0) {
        setResult(data.data);
        toast.success("统一API测试成功！");
      } else {
        toast.error(data.msg || "统一API测试失败");
      }
    } catch (error) {
      console.error('Unified API test failed:', error);
      toast.error("网络错误");
    } finally {
      setLoading(false);
    }
  };

  const handleTestUnifiedNoAuth = async () => {
    if (!prompt.trim()) {
      toast.error("请输入提示词");
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      // 通过测试统一 AI API 调用（绕过认证）
      const response = await fetch('/api/test/replicate-unified', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          prompt,
          options: {
            output_quality: 90,
            output_format: 'webp'
          }
        })
      });

      const data = await response.json();

      if (data.code === 0) {
        setResult(data.data);
        toast.success("统一API测试成功（无认证）！");
      } else {
        toast.error(data.msg || "统一API测试失败（无认证）");
      }
    } catch (error) {
      console.error('Unified API test (no auth) failed:', error);
      toast.error("网络错误");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Replicate Flux Krea Dev 模型测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="prompt">提示词</Label>
            <Input
              id="prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder="输入图像生成提示词..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="apiKey">API Key (用于统一API测试)</Label>
            <Input
              id="apiKey"
              type="password"
              value={apiKey}
              onChange={(e) => setApiKey(e.target.value)}
              placeholder="输入你的 API Key..."
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              onClick={handleTest}
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  测试中...
                </>
              ) : (
                "测试 Replicate Provider"
              )}
            </Button>

            <Button
              onClick={handleTestUnified}
              disabled={loading}
              variant="outline"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  测试中...
                </>
              ) : (
                "测试统一 AI API"
              )}
            </Button>

            <Button
              onClick={handleTestUnifiedNoAuth}
              disabled={loading}
              variant="secondary"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  测试中...
                </>
              ) : (
                "测试统一 API (无认证)"
              )}
            </Button>
          </div>

          {result && (
            <Card>
              <CardHeader>
                <CardTitle>测试结果</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="bg-muted p-4 rounded-md overflow-auto text-sm">
                  {JSON.stringify(result, null, 2)}
                </pre>
                
                {(result.response?.urls || result.urls) && (
                  <div className="mt-4 space-y-2">
                    <h4 className="font-semibold">生成的图片：</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {(result.response?.urls || result.urls || []).map((url: string, index: number) => (
                        <img
                          key={index}
                          src={url}
                          alt={`Generated image ${index + 1}`}
                          className="w-full h-auto rounded-md border"
                        />
                      ))}
                    </div>
                  </div>
                )}

                {result.result?.images && (
                  <div className="mt-4 space-y-2">
                    <h4 className="font-semibold">生成的图片：</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {result.result.images.map((image: any, index: number) => (
                        <img
                          key={index}
                          src={image.url}
                          alt={`Generated image ${index + 1}`}
                          className="w-full h-auto rounded-md border"
                        />
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
