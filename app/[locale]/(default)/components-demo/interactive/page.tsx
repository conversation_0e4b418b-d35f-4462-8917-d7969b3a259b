import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Pricing from "@/components/blocks/pricing";
import Form from "@/components/blocks/form";
import Table from "@/components/blocks/table";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "@/i18n/routing";
import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";
import { Pricing as PricingType } from "@/types/blocks/pricing";
import { FormField } from "@/types/blocks/form";
import { TableColumn } from "@/types/blocks/table";



export default function InteractiveComponentsDemo() {
  // Demo data for CTA component
  const ctaDemo: SectionType = {
    name: "cta",
    title: "Ready to Get Started?",
    description: "Join thousands of developers who are already building amazing applications with our platform.",
    buttons: [
      {
        title: "Start Free Trial",
        url: "/signup",
        variant: "default",
        icon: "RiRocketLine"
      },
      {
        title: "Contact Sales",
        url: "/contact",
        variant: "outline",
        icon: "RiPhoneLine"
      }
    ]
  };

  // Demo data for FAQ component
  const faqDemo: SectionType = {
    name: "faq",
    label: "FAQ",
    title: "Frequently Asked Questions",
    description: "Find answers to common questions about our platform and services.",
    items: [
      {
        title: "How do I get started?",
        description: "Getting started is easy! Simply sign up for a free account, choose your plan, and follow our quick setup guide. You'll be up and running in minutes."
      },
      {
        title: "What technologies do you support?",
        description: "We support all major web technologies including React, Vue, Angular, Node.js, Python, and more. Our platform is designed to work with your existing tech stack."
      },
      {
        title: "Is there a free trial available?",
        description: "Yes! We offer a 14-day free trial with full access to all features. No credit card required to get started."
      },
      {
        title: "How does pricing work?",
        description: "Our pricing is based on usage and features. We offer flexible plans for individuals, teams, and enterprises. Check our pricing page for detailed information."
      },
      {
        title: "Do you offer customer support?",
        description: "Absolutely! We provide 24/7 customer support via email, chat, and phone. Our team is always ready to help you succeed."
      },
      {
        title: "Can I cancel my subscription anytime?",
        description: "Yes, you can cancel your subscription at any time. There are no long-term contracts or cancellation fees."
      }
    ]
  };

  // Demo data for Pricing component
  const pricingDemo: PricingType = {
    name: "pricing",
    title: "Choose Your Plan",
    description: "Select the perfect plan for your needs. Upgrade or downgrade at any time.",
    groups: [
      {
        name: "monthly",
        title: "Monthly"
      },
      {
        name: "yearly",
        title: "Yearly"
      }
    ],
    items: [
      {
        title: "Starter",
        description: "Perfect for individuals and small projects",
        price: "$9",
        original_price: "$19",
        unit: "/month",
        label: "Most Popular",
        is_featured: true,
        features: [
          "Up to 5 projects",
          "10GB storage",
          "Basic support",
          "Community access",
          "SSL certificates"
        ],
        button: {
          title: "Get Started",
          url: "/signup?plan=starter"
        },
        interval: "month",
        product_id: "starter",
        amount: 900,
        currency: "USD"
      },
      {
        title: "Professional",
        description: "Ideal for growing teams and businesses",
        price: "$29",
        unit: "/month",
        features: [
          "Unlimited projects",
          "100GB storage",
          "Priority support",
          "Advanced analytics",
          "Custom domains",
          "Team collaboration"
        ],
        button: {
          title: "Get Started",
          url: "/signup?plan=pro"
        },
        interval: "month",
        product_id: "professional",
        amount: 2900,
        currency: "USD"
      },
      {
        title: "Enterprise",
        description: "For large organizations with custom needs",
        price: "Custom",
        unit: "",
        features: [
          "Everything in Professional",
          "Unlimited storage",
          "24/7 phone support",
          "Custom integrations",
          "SLA guarantee",
          "Dedicated account manager"
        ],
        button: {
          title: "Contact Sales",
          url: "/contact"
        },
        interval: "one-time",
        product_id: "enterprise",
        amount: 0,
        currency: "USD"
      }
    ]
  };

  // Demo data for Form component
  const formFields: FormField[] = [
    {
      name: "name",
      title: "Full Name",
      type: "text",
      placeholder: "Enter your full name",
      validation: { required: true }
    },
    {
      name: "email",
      title: "Email Address",
      type: "email",
      placeholder: "Enter your email address",
      validation: { required: true, email: true }
    },
    {
      name: "company",
      title: "Company",
      type: "text",
      placeholder: "Enter your company name"
    },
    {
      name: "subject",
      title: "Subject",
      type: "select",
      placeholder: "Select a subject",
      validation: { required: true },
      options: [
        { title: "General Inquiry", value: "general" },
        { title: "Technical Support", value: "support" },
        { title: "Sales Question", value: "sales" },
        { title: "Partnership", value: "partnership" }
      ]
    },
    {
      name: "message",
      title: "Message",
      type: "textarea",
      placeholder: "Tell us how we can help you...",
      validation: { required: true }
    }
  ];

  // Demo data for Table component
  const tableColumns: TableColumn[] = [
    { name: "name", title: "Project Name", type: "text" },
    { name: "status", title: "Status", type: "label" },
    { name: "created", title: "Created", type: "time" },
    { name: "updated", title: "Last Updated", type: "time" },
    { name: "actions", title: "Actions", type: "actions" }
  ];

  const tableData = [
    {
      id: "1",
      name: "E-commerce Platform",
      status: "Active",
      created: "2025-01-15",
      updated: "2025-01-19"
    },
    {
      id: "2",
      name: "Mobile App Backend",
      status: "In Progress",
      created: "2025-01-10",
      updated: "2025-01-18"
    },
    {
      id: "3",
      name: "Analytics Dashboard",
      status: "Completed",
      created: "2025-01-05",
      updated: "2025-01-17"
    },
    {
      id: "4",
      name: "API Gateway",
      status: "Paused",
      created: "2025-01-01",
      updated: "2025-01-16"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Page Header */}
      <div className="container mx-auto py-8 px-4">
        <div className="text-center mb-8">
          <Badge variant="outline" className="mb-4">
            Interactive Components
          </Badge>
          <h1 className="text-3xl font-bold mb-4">Interactive Components Demo</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Engage your users with these interactive components: CTAs, FAQs, pricing tables, forms, and data tables.
          </p>
        </div>

        <div className="mb-8">
          <Link href={"/components-demo" as any} className="inline-flex items-center text-primary hover:underline">
            <Icon name="RiArrowLeftLine" className="w-4 h-4 mr-1" />
            Back to Components Demo
          </Link>
        </div>
      </div>

      {/* CTA Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiMegaphoneLine" className="w-5 h-5" />
                Call-to-Action Component
              </CardTitle>
              <CardDescription>
                Encourage user action with compelling CTAs featuring background graphics and multiple buttons.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y">
          <CTA section={ctaDemo} />
        </div>
      </section>

      {/* FAQ Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiQuestionLine" className="w-5 h-5" />
                FAQ Component
              </CardTitle>
              <CardDescription>
                Display frequently asked questions in a clean, numbered format.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y bg-muted/30">
          <FAQ section={faqDemo} />
        </div>
      </section>

      {/* Pricing Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiMoneyDollarCircleLine" className="w-5 h-5" />
                Pricing Component
              </CardTitle>
              <CardDescription>
                Showcase pricing plans with features, billing toggles, and call-to-action buttons.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y">
          <Pricing pricing={pricingDemo} />
        </div>
      </section>

      {/* Form Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiFileTextLine" className="w-5 h-5" />
                Form Component
              </CardTitle>
              <CardDescription>
                Create dynamic forms with various field types, validation, and submission handling.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y bg-muted/30">
          <div className="container mx-auto py-16">
            <div className="max-w-2xl mx-auto">
              <Card>
                <CardHeader>
                  <CardTitle>Contact Us</CardTitle>
                  <CardDescription>
                    Get in touch with our team. We'd love to hear from you!
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Form fields={formFields} />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Table Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiTableLine" className="w-5 h-5" />
                Table Component
              </CardTitle>
              <CardDescription>
                Display data in sortable tables with action buttons and status indicators.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y bg-muted/30">
          <div className="container mx-auto py-16">
            <Card>
              <CardHeader>
                <CardTitle>Recent Projects</CardTitle>
                <CardDescription>
                  Overview of your recent projects and their status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Table columns={tableColumns} data={tableData} />
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  );
}
