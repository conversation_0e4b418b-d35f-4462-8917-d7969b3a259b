"use client";

import Editor from "@/components/blocks/editor";
import MDE<PERSON><PERSON> from "@/components/blocks/mdeditor";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "@/i18n/routing";
import Icon from "@/components/icon";
import { useState } from "react";

export default function EditorsComponentsDemo() {
  const [richTextContent, setRichTextContent] = useState(`
    <h2>Welcome to the Rich Text Editor</h2>
    <p>This is a powerful rich text editor built with <strong>TipTap</strong>. You can:</p>
    <ul>
      <li>Format text with <strong>bold</strong>, <em>italic</em>, and <u>underline</u></li>
      <li>Create lists and organize content</li>
      <li>Add links and images</li>
      <li>Use headings and paragraphs</li>
    </ul>
    <blockquote>
      <p>The editor supports many advanced features for content creation.</p>
    </blockquote>
    <p>Try editing this content to see the editor in action!</p>
  `);

  const [markdownContent, setMarkdownContent] = useState(`# <PERSON>down Editor Demo

Welcome to the **Markdown Editor**! This editor provides a clean interface for writing in Markdown.

## Features

- **Live Preview**: See your formatted content as you type
- **Syntax Highlighting**: Code blocks are highlighted for better readability
- **Toolbar**: Quick access to common formatting options
- **Split View**: Edit and preview side by side

### Code Example

\`\`\`javascript
function greet(name) {
  return \`Hello, \${name}!\`;
}

console.log(greet("World"));
\`\`\`

### Lists and Links

1. **Ordered lists** work great
2. You can also create [links](https://example.com)
3. And add \`inline code\`

- Unordered lists too
- With multiple items
- Easy to create

> Blockquotes are perfect for highlighting important information or quotes.

### Tables

| Feature | Rich Text Editor | Markdown Editor |
|---------|------------------|-----------------|
| WYSIWYG | ✅ | ❌ |
| Markdown | ❌ | ✅ |
| Live Preview | ✅ | ✅ |
| Code Highlighting | ❌ | ✅ |

Try editing this content to explore the Markdown editor features!
`);

  return (
    <div className="min-h-screen bg-background">
      {/* Page Header */}
      <div className="container mx-auto py-8 px-4">
        <div className="text-center mb-8">
          <Badge variant="outline" className="mb-4">
            Editor Components
          </Badge>
          <h1 className="text-3xl font-bold mb-4">Editor Components Demo</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Powerful editing experiences with rich text and Markdown editors for content creation.
          </p>
        </div>

        <div className="mb-8">
          <Link href={"/components-demo" as any} className="inline-flex items-center text-primary hover:underline">
            <Icon name="RiArrowLeftLine" className="w-4 h-4 mr-1" />
            Back to Components Demo
          </Link>
        </div>
      </div>

      {/* Rich Text Editor Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiEditLine" className="w-5 h-5" />
                Rich Text Editor (TipTap)
              </CardTitle>
              <CardDescription>
                WYSIWYG editor with formatting toolbar, perfect for creating rich content with visual feedback.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y bg-muted/30">
          <div className="container mx-auto py-16">
            <div className="max-w-4xl mx-auto">
              <Card>
                <CardHeader>
                  <CardTitle>Rich Text Editor Example</CardTitle>
                  <CardDescription>
                    Edit the content below to see the rich text editor in action
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Editor
                    value={richTextContent}
                    onChange={setRichTextContent}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Markdown Editor Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiMarkdownLine" className="w-5 h-5" />
                Markdown Editor
              </CardTitle>
              <CardDescription>
                Markdown editor with live preview, syntax highlighting, and split-view support.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y">
          <div className="container mx-auto py-16">
            <div className="max-w-6xl mx-auto">
              <Card>
                <CardHeader>
                  <CardTitle>Markdown Editor Example</CardTitle>
                  <CardDescription>
                    Write in Markdown and see the live preview on the right
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <MDEditor
                    value={markdownContent}
                    onChange={setMarkdownContent}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Editor Features Comparison */}
      <section className="mb-16">
        <div className="container mx-auto px-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiCompareLineIcon" className="w-5 h-5" />
                Editor Features Comparison
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                    <Icon name="RiEditLine" className="w-4 h-4" />
                    Rich Text Editor (TipTap)
                  </h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                    <li>WYSIWYG (What You See Is What You Get)</li>
                    <li>Visual formatting toolbar</li>
                    <li>Real-time formatting preview</li>
                    <li>Support for images, links, and media</li>
                    <li>Collaborative editing capabilities</li>
                    <li>Custom extensions and plugins</li>
                    <li>Accessibility features built-in</li>
                    <li>Mobile-friendly interface</li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-3 flex items-center gap-2">
                    <Icon name="RiMarkdownLine" className="w-4 h-4" />
                    Markdown Editor
                  </h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                    <li>Markdown syntax support</li>
                    <li>Live preview with split view</li>
                    <li>Syntax highlighting for code blocks</li>
                    <li>Table editing support</li>
                    <li>Math equation rendering</li>
                    <li>Export to various formats</li>
                    <li>Keyboard shortcuts for efficiency</li>
                    <li>Version control friendly</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Usage Examples */}
      <section className="mb-16">
        <div className="container mx-auto px-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiCodeLine" className="w-5 h-5" />
                Usage Examples
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h4 className="font-semibold mb-3">Rich Text Editor Implementation:</h4>
                <code className="text-sm bg-muted p-4 rounded block whitespace-pre">
{`import Editor from "@/components/blocks/editor";

function MyComponent() {
  const [content, setContent] = useState("");
  
  return (
    <Editor
      content={content}
      onChange={setContent}
      placeholder="Start writing..."
    />
  );
}`}
                </code>
              </div>
              
              <div>
                <h4 className="font-semibold mb-3">Markdown Editor Implementation:</h4>
                <code className="text-sm bg-muted p-4 rounded block whitespace-pre">
{`import MDEditor from "@/components/blocks/mdeditor";

function MyComponent() {
  const [markdown, setMarkdown] = useState("");
  
  return (
    <MDEditor
      value={markdown}
      onChange={setMarkdown}
      height={400}
    />
  );
}`}
                </code>
              </div>

              <div>
                <h4 className="font-semibold mb-3">When to Use Each Editor:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card className="p-4">
                    <h5 className="font-medium mb-2">Rich Text Editor</h5>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Blog posts and articles</li>
                      <li>• Email composition</li>
                      <li>• Content management systems</li>
                      <li>• User-generated content</li>
                      <li>• Non-technical users</li>
                    </ul>
                  </Card>
                  
                  <Card className="p-4">
                    <h5 className="font-medium mb-2">Markdown Editor</h5>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Documentation</li>
                      <li>• README files</li>
                      <li>• Technical writing</li>
                      <li>• Code comments</li>
                      <li>• Developer-focused content</li>
                    </ul>
                  </Card>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
