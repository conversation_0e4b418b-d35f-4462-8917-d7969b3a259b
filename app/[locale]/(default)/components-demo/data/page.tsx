"use client";

import DataCards from "@/components/blocks/data-cards";
import DataCharts from "@/components/blocks/data-charts";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "@/i18n/routing";
import Icon from "@/components/icon";
import { DataCard } from "@/types/blocks/base";

export default function DataComponentsDemo() {
  // Demo data for Data Cards component
  const dataCardsDemo: DataCard[] = [
    {
      title: "Total Revenue",
      value: "$45,231.89",
      label: "+20.1%",
      description: "from last month",
      tip: "Revenue generated this month",
      icon: "RiMoneyDollarCircleLine"
    },
    {
      title: "Active Users",
      value: "2,350",
      label: "+180.1%",
      description: "from last month",
      tip: "Users who logged in this month",
      icon: "RiUserLine"
    },
    {
      title: "Total Orders",
      value: "12,234",
      label: "+19%",
      description: "from last month",
      tip: "Orders placed this month",
      icon: "RiShoppingCartLine"
    },
    {
      title: "Conversion Rate",
      value: "3.2%",
      label: "+0.5%",
      description: "from last month",
      tip: "Percentage of visitors who made a purchase",
      icon: "RiLineChartLine"
    }
  ];

  // Demo data for Data Charts component
  const chartData = [
    { date: "2024-01-01", desktop: 222, mobile: 150 },
    { date: "2024-01-02", desktop: 97, mobile: 180 },
    { date: "2024-01-03", desktop: 167, mobile: 120 },
    { date: "2024-01-04", desktop: 242, mobile: 260 },
    { date: "2024-01-05", desktop: 373, mobile: 290 },
    { date: "2024-01-06", desktop: 301, mobile: 340 },
    { date: "2024-01-07", desktop: 245, mobile: 180 },
    { date: "2024-01-08", desktop: 409, mobile: 320 },
    { date: "2024-01-09", desktop: 59, mobile: 110 },
    { date: "2024-01-10", desktop: 261, mobile: 190 },
    { date: "2024-01-11", desktop: 327, mobile: 350 },
    { date: "2024-01-12", desktop: 292, mobile: 210 },
    { date: "2024-01-13", desktop: 342, mobile: 380 },
    { date: "2024-01-14", desktop: 137, mobile: 220 },
    { date: "2024-01-15", desktop: 120, mobile: 170 },
    { date: "2024-01-16", desktop: 138, mobile: 190 },
    { date: "2024-01-17", desktop: 446, mobile: 360 },
    { date: "2024-01-18", desktop: 364, mobile: 410 },
    { date: "2024-01-19", desktop: 243, mobile: 180 },
    { date: "2024-01-20", desktop: 89, mobile: 150 },
    { date: "2024-01-21", desktop: 137, mobile: 200 },
    { date: "2024-01-22", desktop: 224, mobile: 170 },
    { date: "2024-01-23", desktop: 138, mobile: 230 },
    { date: "2024-01-24", desktop: 387, mobile: 290 },
    { date: "2024-01-25", desktop: 215, mobile: 250 },
    { date: "2024-01-26", desktop: 75, mobile: 130 },
    { date: "2024-01-27", desktop: 383, mobile: 420 },
    { date: "2024-01-28", desktop: 122, mobile: 180 },
    { date: "2024-01-29", desktop: 315, mobile: 240 },
    { date: "2024-01-30", desktop: 454, mobile: 380 }
  ];

  const chartFields = [
    {
      key: "desktop",
      label: "Desktop",
      color: "hsl(var(--chart-1))"
    },
    {
      key: "mobile",
      label: "Mobile",
      color: "hsl(var(--chart-2))"
    }
  ];

  const chartProps = {
    title: "Website Traffic",
    description: "Showing total visitors for the last 30 days",
    data: chartData,
    fields: chartFields,
    defaultTimeRange: "30d" as const
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Page Header */}
      <div className="container mx-auto py-8 px-4">
        <div className="text-center mb-8">
          <Badge variant="outline" className="mb-4">
            Data Visualization Components
          </Badge>
          <h1 className="text-3xl font-bold mb-4">Data Visualization Components Demo</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Display your data beautifully with cards and interactive charts that provide insights at a glance.
          </p>
        </div>

        <div className="mb-8">
          <Link href={"/components-demo" as any} className="inline-flex items-center text-primary hover:underline">
            <Icon name="RiArrowLeftLine" className="w-4 h-4 mr-1" />
            Back to Components Demo
          </Link>
        </div>
      </div>

      {/* Data Cards Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiDashboardLine" className="w-5 h-5" />
                Data Cards Component
              </CardTitle>
              <CardDescription>
                Display key metrics and KPIs in visually appealing cards with icons, values, and trend indicators.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y bg-muted/30">
          <div className="container mx-auto py-16">
            <DataCards dataCards={dataCardsDemo} />
          </div>
        </div>
      </section>

      {/* Data Charts Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiLineChartLine" className="w-5 h-5" />
                Data Charts Component
              </CardTitle>
              <CardDescription>
                Interactive area charts with time range selection and category filtering for data visualization.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y">
          <div className="container mx-auto py-16">
            <div className="max-w-4xl mx-auto">
              <DataCharts {...chartProps} />
            </div>
          </div>
        </div>
      </section>

      {/* Additional Data Examples */}
      <section className="mb-16">
        <div className="container mx-auto px-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiInformationLine" className="w-5 h-5" />
                Data Component Features
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h4 className="font-semibold mb-3">Data Cards Features:</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>Responsive grid layout that adapts to screen size</li>
                  <li>Support for icons, values, labels, and descriptions</li>
                  <li>Trend indicators with percentage changes</li>
                  <li>Tooltips for additional context</li>
                  <li>Gradient backgrounds and shadow effects</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-semibold mb-3">Data Charts Features:</h4>
                <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                  <li>Interactive area charts built with Recharts</li>
                  <li>Time range selection (30d, 7d, 24h)</li>
                  <li>Category filtering and toggling</li>
                  <li>Responsive design for mobile and desktop</li>
                  <li>Customizable colors and styling</li>
                  <li>Tooltip with detailed data on hover</li>
                </ul>
              </div>

              <div>
                <h4 className="font-semibold mb-3">Usage Examples:</h4>
                <div className="space-y-2">
                  <code className="text-sm bg-muted p-2 rounded block">
                    {`<DataCards dataCards={metricsData} />`}
                  </code>
                  <code className="text-sm bg-muted p-2 rounded block">
                    {`<DataCharts data={chartData} config={chartConfig} />`}
                  </code>
                </div>
              </div>

              <div>
                <h4 className="font-semibold mb-3">Data Structure:</h4>
                <div className="space-y-2">
                  <p className="text-sm text-muted-foreground">Data Cards expect an array of objects with:</p>
                  <code className="text-sm bg-muted p-2 rounded block">
                    {`{ title, value, label, description, tip, icon }`}
                  </code>
                  <p className="text-sm text-muted-foreground">Charts expect time-series data with:</p>
                  <code className="text-sm bg-muted p-2 rounded block">
                    {`{ date, [category1], [category2], ... }`}
                  </code>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Performance Metrics Example */}
      <section className="mb-16">
        <div className="container mx-auto px-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiSpeedUpLine" className="w-5 h-5" />
                Performance Metrics Example
              </CardTitle>
              <CardDescription>
                Another example showing different types of metrics and data visualization.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardDescription>Page Load Time</CardDescription>
                    <CardTitle className="text-2xl">1.2s</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Badge variant="outline" className="bg-green-50 border-green-200 text-green-800">
                      -15%
                    </Badge>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardDescription>Bounce Rate</CardDescription>
                    <CardTitle className="text-2xl">23.1%</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Badge variant="outline" className="bg-red-50 border-red-200 text-red-800">
                      +2.1%
                    </Badge>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardDescription>API Response</CardDescription>
                    <CardTitle className="text-2xl">245ms</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Badge variant="outline" className="bg-blue-50 border-blue-200 text-blue-800">
                      -8%
                    </Badge>
                  </CardContent>
                </Card>
                
                <Card>
                  <CardHeader className="pb-2">
                    <CardDescription>Uptime</CardDescription>
                    <CardTitle className="text-2xl">99.9%</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Badge variant="outline" className="bg-green-50 border-green-200 text-green-800">
                      Stable
                    </Badge>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
