import Hero from "@/components/blocks/hero";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Showcase from "@/components/blocks/showcase";
import Showcase1 from "@/components/blocks/showcase1";
import Blog from "@/components/blocks/blog";
import Testimonial from "@/components/blocks/testimonial";
import Stats from "@/components/blocks/stats";
import Branding from "@/components/blocks/branding";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "@/i18n/routing";
import Icon from "@/components/icon";
import { Hero as HeroType } from "@/types/blocks/hero";
import { Section as SectionType } from "@/types/blocks/section";
import { Blog as BlogType } from "@/types/blocks/blog";



export default function ContentComponentsDemo() {
  // Demo data for Hero component
  const heroDemo: HeroType = {
    title: "Build Amazing Applications with Modern Tools",
    highlight_text: "Amazing Applications",
    description: "Create powerful, scalable applications using the latest technologies and best practices. <br/>Ship faster with our comprehensive toolkit.",
    announcement: {
      label: "New",
      title: "🚀 Version 2.0 is here!",
      url: "#features"
    },
    tip: "🎁 Free trial available",
    buttons: [
      {
        title: "Get Started",
        icon: "RiRocketLine",
        url: "#pricing",
        variant: "default"
      },
      {
        title: "View Demo",
        icon: "RiPlayLine",
        url: "#demo",
        variant: "outline"
      }
    ],
    show_happy_users: true,
    show_badge: true
  };

  // Demo data for Feature component
  const featureDemo: SectionType = {
    name: "features",
    title: "Powerful Features for Modern Development",
    description: "Everything you need to build, deploy, and scale your applications efficiently.",
    items: [
      {
        title: "Fast Development",
        description: "Accelerate your development process with pre-built components and templates.",
        icon: "RiFlashlightLine"
      },
      {
        title: "Scalable Architecture",
        description: "Built with scalability in mind to grow with your business needs.",
        icon: "RiExpandUpDownLine"
      },
      {
        title: "Modern Stack",
        description: "Leverage the latest technologies and frameworks for optimal performance.",
        icon: "RiStackLine"
      },
      {
        title: "Developer Experience",
        description: "Optimized for developer productivity with excellent tooling and documentation.",
        icon: "RiCodeLine"
      },
      {
        title: "Security First",
        description: "Built-in security best practices to protect your applications and data.",
        icon: "RiShieldCheckLine"
      },
      {
        title: "24/7 Support",
        description: "Get help when you need it with our dedicated support team.",
        icon: "RiCustomerServiceLine"
      }
    ]
  };

  // Demo data for Feature1 component (with image)
  const feature1Demo: SectionType = {
    name: "feature1",
    title: "Intuitive Dashboard",
    description: "Monitor your applications with our comprehensive dashboard. Get real-time insights, track performance metrics, and manage your resources efficiently.",
    image: {
      src: "/imgs/dashboard-preview.jpg",
      alt: "Dashboard Preview"
    },
    buttons: [
      {
        title: "Explore Dashboard",
        url: "#dashboard",
        variant: "default",
        icon: "RiDashboardLine"
      }
    ]
  };

  // Demo data for Stats component
  const statsDemo: SectionType = {
    name: "stats",
    label: "Trusted by developers worldwide",
    title: "Numbers That Matter",
    description: "See how our platform is making a difference for developers and businesses globally.",
    icon: "RiBarChartLine",
    items: [
      {
        title: "Active Users",
        label: "50K+",
        description: "Developers using our platform"
      },
      {
        title: "Projects Built",
        label: "100K+",
        description: "Successful deployments"
      },
      {
        title: "Uptime",
        label: "99.9%",
        description: "Reliable service guarantee"
      }
    ]
  };

  // Demo data for Testimonial component
  const testimonialDemo: SectionType = {
    name: "testimonials",
    title: "What Developers Say",
    description: "Hear from the developers who are building amazing things with our platform.",
    items: [
      {
        title: "Sarah Chen",
        label: "Senior Developer",
        description: "This platform has completely transformed how we build and deploy applications. The developer experience is outstanding!",
        image: {
          src: "/imgs/users/1.png"
        }
      },
      {
        title: "Mike Johnson",
        label: "Tech Lead",
        description: "The scalability and performance we've achieved using this platform is incredible. Highly recommended for any serious project.",
        image: {
          src: "/imgs/users/2.png"
        }
      },
      {
        title: "Emily Rodriguez",
        label: "Full Stack Developer",
        description: "From prototype to production in record time. The tools and components provided are exactly what modern development needs.",
        image: {
          src: "/imgs/users/3.png"
        }
      }
    ]
  };

  // Demo data for Feature2 component (interactive with accordion/carousel)
  const feature2Demo: SectionType = {
    name: "feature2",
    label: "Interactive Features",
    title: "Advanced Development Tools",
    description: "Explore our comprehensive suite of development tools designed to streamline your workflow and boost productivity.",
    items: [
      {
        title: "Code Editor",
        description: "Advanced code editor with syntax highlighting, auto-completion, and real-time collaboration features.",
        image: {
          src: "/imgs/features/code-editor.jpg",
          alt: "Code Editor"
        }
      },
      {
        title: "Debugging Tools",
        description: "Powerful debugging capabilities with breakpoints, variable inspection, and step-through execution.",
        image: {
          src: "/imgs/features/debugging.jpg",
          alt: "Debugging Tools"
        }
      },
      {
        title: "Performance Monitor",
        description: "Real-time performance monitoring with detailed analytics and optimization suggestions.",
        image: {
          src: "/imgs/features/performance.jpg",
          alt: "Performance Monitor"
        }
      }
    ]
  };

  // Demo data for Feature3 component (tabbed interface)
  const feature3Demo: SectionType = {
    name: "feature3",
    label: "Development Workflow",
    title: "Complete Development Pipeline",
    description: "From planning to deployment, our platform covers every aspect of the development lifecycle.",
    items: [
      {
        title: "Planning & Design",
        description: "Collaborative planning tools with wireframing, user story mapping, and design systems.",
        image: {
          src: "/imgs/workflow/planning.jpg",
          alt: "Planning & Design"
        }
      },
      {
        title: "Development",
        description: "Integrated development environment with version control, code review, and automated testing.",
        image: {
          src: "/imgs/workflow/development.jpg",
          alt: "Development"
        }
      },
      {
        title: "Testing & QA",
        description: "Comprehensive testing suite with unit tests, integration tests, and automated QA workflows.",
        image: {
          src: "/imgs/workflow/testing.jpg",
          alt: "Testing & QA"
        }
      },
      {
        title: "Deployment",
        description: "Seamless deployment pipeline with CI/CD integration and multi-environment support.",
        image: {
          src: "/imgs/workflow/deployment.jpg",
          alt: "Deployment"
        }
      }
    ]
  };

  // Demo data for Showcase component
  const showcaseDemo: SectionType = {
    name: "showcase",
    title: "Featured Projects",
    description: "Discover amazing projects built by our community using our platform and tools.",
    items: [
      {
        title: "E-commerce Platform",
        description: "A modern e-commerce solution with advanced features like real-time inventory, AI-powered recommendations, and seamless payment integration.",
        url: "#project1",
        image: {
          src: "/imgs/showcase/ecommerce.jpg",
          alt: "E-commerce Platform"
        }
      },
      {
        title: "Social Media Dashboard",
        description: "Comprehensive social media management tool with analytics, scheduling, and multi-platform integration.",
        url: "#project2",
        image: {
          src: "/imgs/showcase/social-dashboard.jpg",
          alt: "Social Media Dashboard"
        }
      },
      {
        title: "Learning Management System",
        description: "Interactive learning platform with video streaming, progress tracking, and collaborative features.",
        url: "#project3",
        image: {
          src: "/imgs/showcase/lms.jpg",
          alt: "Learning Management System"
        }
      },
      {
        title: "Healthcare App",
        description: "Patient management system with appointment scheduling, medical records, and telemedicine capabilities.",
        url: "#project4",
        image: {
          src: "/imgs/showcase/healthcare.jpg",
          alt: "Healthcare App"
        }
      },
      {
        title: "Financial Dashboard",
        description: "Real-time financial analytics platform with portfolio management and risk assessment tools.",
        url: "#project5",
        image: {
          src: "/imgs/showcase/finance.jpg",
          alt: "Financial Dashboard"
        }
      },
      {
        title: "IoT Monitoring System",
        description: "Industrial IoT platform for monitoring sensors, analyzing data, and managing connected devices.",
        url: "#project6",
        image: {
          src: "/imgs/showcase/iot.jpg",
          alt: "IoT Monitoring System"
        }
      }
    ]
  };

  // Demo data for Branding component
  const brandingDemo: SectionType = {
    title: "Built on Industry Standards",
    items: [
      {
        title: "React",
        image: {
          src: "/imgs/logos/react.svg",
          alt: "React"
        }
      },
      {
        title: "Next.js",
        image: {
          src: "/imgs/logos/nextjs.svg",
          alt: "Next.js"
        }
      },
      {
        title: "TypeScript",
        image: {
          src: "/imgs/logos/typescript.svg",
          alt: "TypeScript"
        }
      },
      {
        title: "Tailwind CSS",
        image: {
          src: "/imgs/logos/tailwindcss.svg",
          alt: "Tailwind CSS"
        }
      }
    ]
  };

  // Demo data for Showcase1 component (alternative showcase with navigation)
  const showcase1Demo: SectionType = {
    name: "showcase1",
    title: "Success Stories",
    description: "See how businesses are transforming their operations with our solutions.",
    items: [
      {
        title: "TechCorp Solutions",
        description: "Increased development velocity by 300% and reduced deployment time from hours to minutes using our CI/CD platform.",
        image: {
          src: "/imgs/success/techcorp.jpg",
          alt: "TechCorp Solutions"
        }
      },
      {
        title: "StartupXYZ",
        description: "Scaled from 0 to 1M users in 6 months with our scalable infrastructure and monitoring tools.",
        image: {
          src: "/imgs/success/startupxyz.jpg",
          alt: "StartupXYZ"
        }
      },
      {
        title: "Enterprise Inc",
        description: "Modernized legacy systems and improved security compliance while reducing operational costs by 40%.",
        image: {
          src: "/imgs/success/enterprise.jpg",
          alt: "Enterprise Inc"
        }
      }
    ]
  };

  // Demo data for Blog component
  const blogDemo: BlogType = {
    name: "blog",
    label: "Latest Updates",
    title: "From Our Blog",
    description: "Stay updated with the latest news, tutorials, and insights from our team.",
    items: [
      {
        slug: "getting-started-guide",
        title: "Complete Getting Started Guide",
        description: "Everything you need to know to get up and running with our platform in under 30 minutes.",
        author_name: "Sarah Johnson",
        author_avatar_url: "/imgs/authors/sarah.jpg",
        created_at: "2025-01-15",
        cover_url: "/imgs/blog/getting-started.jpg",
        locale: "en"
      },
      {
        slug: "best-practices-2025",
        title: "Development Best Practices for 2025",
        description: "Learn the latest best practices and patterns that will make your development process more efficient.",
        author_name: "Mike Chen",
        author_avatar_url: "/imgs/authors/mike.jpg",
        created_at: "2025-01-12",
        cover_url: "/imgs/blog/best-practices.jpg",
        locale: "en"
      },
      {
        slug: "performance-optimization",
        title: "Advanced Performance Optimization Techniques",
        description: "Deep dive into performance optimization strategies that can dramatically improve your application speed.",
        author_name: "Emily Rodriguez",
        author_avatar_url: "/imgs/authors/emily.jpg",
        created_at: "2025-01-10",
        cover_url: "/imgs/blog/performance.jpg",
        locale: "en"
      }
    ]
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Page Header */}
      <div className="container mx-auto py-8 px-4">
        <div className="text-center mb-8">
          <Badge variant="outline" className="mb-4">
            Content Display Components
          </Badge>
          <h1 className="text-3xl font-bold mb-4">Content Display Components Demo</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Showcase your content with these powerful display components: heroes, features, testimonials, and more.
          </p>
        </div>

        <div className="mb-8">
          <Link href={"/components-demo" as any} className="inline-flex items-center text-primary hover:underline">
            <Icon name="RiArrowLeftLine" className="w-4 h-4 mr-1" />
            Back to Components Demo
          </Link>
        </div>
      </div>

      {/* Hero Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiStarLine" className="w-5 h-5" />
                Hero Component
              </CardTitle>
              <CardDescription>
                Main hero section with title, description, buttons, and optional announcements.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y">
          <Hero hero={heroDemo} />
        </div>
      </section>

      {/* Feature Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiGridLine" className="w-5 h-5" />
                Feature Grid Component
              </CardTitle>
              <CardDescription>
                Display features in a clean grid layout with icons and descriptions.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y bg-muted/30">
          <Feature section={featureDemo} />
        </div>
      </section>

      {/* Feature1 Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiImageLine" className="w-5 h-5" />
                Feature with Image Component
              </CardTitle>
              <CardDescription>
                Feature section with side-by-side image and content layout.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y">
          <Feature1 section={feature1Demo} />
        </div>
      </section>

      {/* Stats Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiBarChartLine" className="w-5 h-5" />
                Stats Component
              </CardTitle>
              <CardDescription>
                Display key metrics and statistics in an eye-catching format.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y bg-muted/30">
          <Stats section={statsDemo} />
        </div>
      </section>

      {/* Testimonial Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiChatQuoteLine" className="w-5 h-5" />
                Testimonial Component
              </CardTitle>
              <CardDescription>
                Showcase customer testimonials with auto-scrolling carousel.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y">
          <Testimonial section={testimonialDemo} />
        </div>
      </section>

      {/* Feature2 Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiInteractionLine" className="w-5 h-5" />
                Feature2 Component (Interactive)
              </CardTitle>
              <CardDescription>
                Interactive feature component with accordion and carousel synchronization.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y">
          <Feature2 section={feature2Demo} />
        </div>
      </section>

      {/* Feature3 Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiTabLine" className="w-5 h-5" />
                Feature3 Component (Tabbed)
              </CardTitle>
              <CardDescription>
                Tabbed feature component with step-by-step workflow visualization.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y bg-muted/30">
          <Feature3 section={feature3Demo} />
        </div>
      </section>

      {/* Showcase Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiGalleryLine" className="w-5 h-5" />
                Showcase Component
              </CardTitle>
              <CardDescription>
                Grid-based showcase for displaying projects, portfolios, or featured content.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y">
          <Showcase section={showcaseDemo} />
        </div>
      </section>

      {/* Showcase1 Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiSlideshow3Line" className="w-5 h-5" />
                Showcase1 Component (Alternative)
              </CardTitle>
              <CardDescription>
                Alternative showcase component with navigation controls and different layout.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y bg-muted/30">
          <Showcase1 section={showcase1Demo} />
        </div>
      </section>

      {/* Blog Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiArticleLine" className="w-5 h-5" />
                Blog Component
              </CardTitle>
              <CardDescription>
                Blog posts display with author information, dates, and featured images.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y">
          <Blog blog={blogDemo} />
        </div>
      </section>

      {/* Branding Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiBrandingLine" className="w-5 h-5" />
                Branding Component
              </CardTitle>
              <CardDescription>
                Display partner logos, technology stack, or brand associations.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>
        <div className="border-y bg-muted/30">
          <Branding section={brandingDemo} />
        </div>
      </section>
    </div>
  );
}
