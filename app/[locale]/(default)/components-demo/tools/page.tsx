import Toolbar from "@/components/blocks/toolbar";
import Empty from "@/components/blocks/empty";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "@/i18n/routing";
import Icon from "@/components/icon";
import { Button as ButtonType } from "@/types/blocks/base";



export default function ToolsComponentsDemo() {
  // Demo data for Toolbar component
  const toolbarItems: ButtonType[] = [
    {
      title: "New Project",
      icon: "RiAddLine",
      url: "/projects/new",
      variant: "default"
    },
    {
      title: "Import",
      icon: "RiUploadLine",
      url: "/import",
      variant: "outline"
    },
    {
      title: "Export",
      icon: "RiDownloadLine",
      url: "/export",
      variant: "outline"
    },
    {
      title: "Settings",
      icon: "RiSettingsLine",
      url: "/settings",
      variant: "ghost"
    },
    {
      title: "Help",
      icon: "RiQuestionLine",
      url: "/help",
      variant: "ghost"
    }
  ];

  // Demo data for different toolbar configurations
  const primaryToolbar: ButtonType[] = [
    {
      title: "Save",
      icon: "RiSaveLine",
      variant: "default"
    },
    {
      title: "Undo",
      icon: "RiArrowGoBackLine",
      variant: "outline"
    },
    {
      title: "Redo",
      icon: "RiArrowGoForwardLine",
      variant: "outline"
    }
  ];

  const secondaryToolbar: ButtonType[] = [
    {
      title: "Copy",
      icon: "RiFileCopyLine",
      variant: "ghost"
    },
    {
      title: "Paste",
      icon: "RiClipboardLine",
      variant: "ghost"
    },
    {
      title: "Delete",
      icon: "RiDeleteBinLine",
      variant: "ghost"
    }
  ];

  const adminToolbar: ButtonType[] = [
    {
      title: "Add User",
      icon: "RiUserAddLine",
      url: "/admin/users/add",
      variant: "default"
    },
    {
      title: "Bulk Actions",
      icon: "RiCheckboxMultipleLine",
      variant: "outline"
    },
    {
      title: "Export Data",
      icon: "RiFileExcelLine",
      url: "/admin/export",
      variant: "outline"
    },
    {
      title: "System Logs",
      icon: "RiFileListLine",
      url: "/admin/logs",
      variant: "ghost"
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      {/* Page Header */}
      <div className="container mx-auto py-8 px-4">
        <div className="text-center mb-8">
          <Badge variant="outline" className="mb-4">
            Utility Components
          </Badge>
          <h1 className="text-3xl font-bold mb-4">Utility Components Demo</h1>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Essential utility components for toolbars, empty states, and other interface elements.
          </p>
        </div>

        <div className="mb-8">
          <Link href={"/components-demo" as any} className="inline-flex items-center text-primary hover:underline">
            <Icon name="RiArrowLeftLine" className="w-4 h-4 mr-1" />
            Back to Components Demo
          </Link>
        </div>
      </div>

      {/* Toolbar Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiToolsLine" className="w-5 h-5" />
                Toolbar Component
              </CardTitle>
              <CardDescription>
                Flexible toolbar component for displaying action buttons with various styles and configurations.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        <div className="space-y-8">
          {/* Main Toolbar Example */}
          <div className="container mx-auto px-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Main Toolbar Example</CardTitle>
                <CardDescription>
                  A comprehensive toolbar with primary actions for project management
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-4 border rounded-lg bg-muted/30">
                  <Toolbar items={toolbarItems} />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Editor Toolbar Example */}
          <div className="container mx-auto px-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Editor Toolbar Example</CardTitle>
                <CardDescription>
                  Simple toolbar for editor actions like save, undo, and redo
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-4 border rounded-lg bg-muted/30">
                  <Toolbar items={primaryToolbar} />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Secondary Actions Toolbar */}
          <div className="container mx-auto px-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Secondary Actions Toolbar</CardTitle>
                <CardDescription>
                  Toolbar with ghost variant buttons for secondary actions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-4 border rounded-lg bg-muted/30">
                  <Toolbar items={secondaryToolbar} />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Admin Toolbar Example */}
          <div className="container mx-auto px-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Admin Toolbar Example</CardTitle>
                <CardDescription>
                  Administrative toolbar with mixed button variants and actions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="p-4 border rounded-lg bg-muted/30">
                  <Toolbar items={adminToolbar} />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Empty State Component Demo */}
      <section className="mb-16">
        <div className="container mx-auto px-4 mb-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiInboxLine" className="w-5 h-5" />
                Empty State Component
              </CardTitle>
              <CardDescription>
                Display helpful empty states when there's no content to show, with optional actions.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        <div className="space-y-8">
          {/* Basic Empty State */}
          <div className="container mx-auto px-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Basic Empty State</CardTitle>
                <CardDescription>
                  Simple empty state with icon, title, and description
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg bg-muted/30 min-h-[300px] flex items-center justify-center">
                  <Empty message="No items found. There are no items to display at the moment." />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Empty State with Action */}
          <div className="container mx-auto px-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Empty State with Action</CardTitle>
                <CardDescription>
                  Empty state with a call-to-action button to help users get started
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg bg-muted/30 min-h-[300px] flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <Icon name="RiFileAddLine" className="w-12 h-12 mx-auto text-muted-foreground" />
                    <div>
                      <h3 className="text-lg font-semibold">No projects yet</h3>
                      <p className="text-muted-foreground">Create your first project to get started with building amazing applications.</p>
                    </div>
                    <Button variant="default" className="mt-4">
                      <Icon name="RiAddLine" className="w-4 h-4 mr-2" />
                      Create Project
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Search Empty State */}
          <div className="container mx-auto px-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Search Empty State</CardTitle>
                <CardDescription>
                  Empty state for search results with suggestions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg bg-muted/30 min-h-[300px] flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <Icon name="RiSearchLine" className="w-12 h-12 mx-auto text-muted-foreground" />
                    <div>
                      <h3 className="text-lg font-semibold">No search results</h3>
                      <p className="text-muted-foreground">We couldn't find any results for your search. Try adjusting your search terms or browse our categories.</p>
                    </div>
                    <Button variant="outline" className="mt-4">
                      <Icon name="RiGridLine" className="w-4 h-4 mr-2" />
                      Browse Categories
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Error Empty State */}
          <div className="container mx-auto px-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Error Empty State</CardTitle>
                <CardDescription>
                  Empty state for error scenarios with retry action
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="border rounded-lg bg-muted/30 min-h-[300px] flex items-center justify-center">
                  <div className="text-center space-y-4">
                    <Icon name="RiErrorWarningLine" className="w-12 h-12 mx-auto text-destructive" />
                    <div>
                      <h3 className="text-lg font-semibold">Something went wrong</h3>
                      <p className="text-muted-foreground">We encountered an error while loading your data. Please try again or contact support if the problem persists.</p>
                    </div>
                    <Button variant="default" className="mt-4">
                      <Icon name="RiRefreshLine" className="w-4 h-4 mr-2" />
                      Try Again
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Component Usage Information */}
      <section className="mb-16">
        <div className="container mx-auto px-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Icon name="RiCodeLine" className="w-5 h-5" />
                Usage Examples
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h4 className="font-semibold mb-3">Toolbar Component:</h4>
                <code className="text-sm bg-muted p-4 rounded block whitespace-pre">
{`import Toolbar from "@/components/blocks/toolbar";

const toolbarItems = [
  {
    title: "New",
    icon: "RiAddLine",
    url: "/new",
    variant: "default"
  },
  {
    title: "Edit",
    icon: "RiEditLine",
    variant: "outline"
  }
];

<Toolbar items={toolbarItems} />`}
                </code>
              </div>
              
              <div>
                <h4 className="font-semibold mb-3">Empty State Component:</h4>
                <code className="text-sm bg-muted p-4 rounded block whitespace-pre">
{`import Empty from "@/components/blocks/empty";

<Empty
  icon="RiInboxLine"
  title="No items found"
  description="There are no items to display."
  action={{
    title: "Add Item",
    url: "/add",
    variant: "default"
  }}
/>`}
                </code>
              </div>

              <div>
                <h4 className="font-semibold mb-3">Common Use Cases:</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h5 className="font-medium mb-2">Toolbar:</h5>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Page headers with actions</li>
                      <li>• Data table controls</li>
                      <li>• Editor toolbars</li>
                      <li>• Admin panel actions</li>
                      <li>• Form submission areas</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h5 className="font-medium mb-2">Empty State:</h5>
                    <ul className="text-sm text-muted-foreground space-y-1">
                      <li>• Empty data tables</li>
                      <li>• No search results</li>
                      <li>• First-time user experience</li>
                      <li>• Error states</li>
                      <li>• Loading placeholders</li>
                    </ul>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  );
}
