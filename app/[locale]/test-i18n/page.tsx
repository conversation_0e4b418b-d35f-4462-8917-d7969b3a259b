"use client";

import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import I18nLanguageSwitchDialog from "@/components/language/i18n-switch-dialog";
import Schema from "@/components/seo";


export default function TestI18nPage() {
  const t = useTranslations("language_switch");
  const [showDialog, setShowDialog] = useState(false);

  return (
<>
 <Schema
        title="超级马里奥兄弟"
        description="经典的平台跳跃游戏，与马里奥一起冒险拯救公主"
        image="https://example.com/mario.jpg"
        url="https://example.com/games/super-mario"
        schemaType="SoftwareApplication"
        applicationCategory="GameApplication"
        operatingSystem="Nintendo Switch, PC"
        ratingValue={4.8}
        ratingCount={250}
        breadcrumb={[
          { name: "游戏", url: "/games" },
          { name: "平台游戏", url: "/games/platform" }
        ]}
      />
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">国际化文本测试</h1>
        <p className="text-muted-foreground">测试 useTranslations 钩子是否正常工作</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>翻译文本测试</CardTitle>
          <CardDescription>直接使用 useTranslations 获取的文本</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <strong>title:</strong> {t("title")}
          </div>
          <div>
            <strong>description (with params):</strong> {t("description", { currentLanguage: "English", suggestedLanguage: "中文" })}
          </div>
          <div>
            <strong>switch_button (with params):</strong> {t("switch_button", { suggestedLanguage: "中文" })}
          </div>
          <div>
            <strong>cancel_button (with params):</strong> {t("cancel_button", { currentLanguage: "English" })}
          </div>

          <div className="mt-4 p-4 bg-muted rounded">
            <h4 className="font-semibold mb-2">原始翻译文本（不带参数）：</h4>
            <div className="space-y-1 text-sm">
              <div><strong>description:</strong> {t.raw("description")}</div>
              <div><strong>switch_button:</strong> {t.raw("switch_button")}</div>
              <div><strong>cancel_button:</strong> {t.raw("cancel_button")}</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>弹框组件测试</CardTitle>
          <CardDescription>测试语言切换弹框组件</CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={() => setShowDialog(true)}>
            显示语言切换弹框
          </Button>
        </CardContent>
      </Card>

      <I18nLanguageSwitchDialog
        open={showDialog}
        currentLanguage="en"
        suggestedLanguage="zh"
        onSwitch={() => {
          console.log("用户选择切换语言");
          setShowDialog(false);
        }}
        onCancel={() => {
          console.log("用户选择保持当前语言");
          setShowDialog(false);
        }}
        onClose={() => setShowDialog(false)}
      />
    </div>
    </>
  );
}
