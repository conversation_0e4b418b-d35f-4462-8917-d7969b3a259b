"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { MessageSquare, Image, Video } from "lucide-react";
import { useTranslations } from "next-intl";
import { useDeviceLayout } from "../../hooks/use-device-layout";  // 优化：导入设备布局hook

interface ModelTypeTabsProps {
  activeTab: string;
  onTabChange: (value: string) => void;
}

export function ModelTypeTabs({ activeTab, onTabChange }: ModelTypeTabsProps) {
  const t = useTranslations("ai-dashboard.tabs");
  const { isMobile, isSmallMobile } = useDeviceLayout();  // 优化：获取设备状态

  return (
    <Tabs value={activeTab} onValueChange={onTabChange} className="w-full max-w-full overflow-x-hidden">
      <TabsList className={`grid w-full max-w-full grid-cols-3 bg-gradient-to-r from-muted/50 to-muted/30 ${
        isSmallMobile ? 'mb-1 h-7' :  // 优化：超小屏幕最小下边距和高度
        isMobile ? 'mb-1 h-8' : 'mb-6 h-10'  // 优化：移动端减少下边距和高度
      }`}>
        <TabsTrigger
          value="text"
          className={`flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white ${
            isSmallMobile ? 'gap-1 text-xs px-1' :  // 优化：超小屏幕减少间距、字体和内边距
            isMobile ? 'gap-1.5 text-sm px-2' : 'gap-2 text-base px-3'
          }`}
        >
          <MessageSquare className={`${
            isSmallMobile ? 'w-3 h-3' :  // 优化：超小屏幕减少图标大小
            isMobile ? 'w-3.5 h-3.5' : 'w-4 h-4'
          }`} />
          {isSmallMobile ? t("text").slice(0, 3) : t("text")}  {/* 优化：超小屏幕简化文字 */}
        </TabsTrigger>
        <TabsTrigger
          value="image"
          className={`flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-teal-500 data-[state=active]:text-white ${
            isSmallMobile ? 'gap-1 text-xs px-1' :  // 优化：超小屏幕减少间距、字体和内边距
            isMobile ? 'gap-1.5 text-sm px-2' : 'gap-2 text-base px-3'
          }`}
        >
          <Image className={`${
            isSmallMobile ? 'w-3 h-3' :  // 优化：超小屏幕减少图标大小
            isMobile ? 'w-3.5 h-3.5' : 'w-4 h-4'
          }`} />
          {isSmallMobile ? t("image").slice(0, 3) : t("image")}  {/* 优化：超小屏幕简化文字 */}
        </TabsTrigger>
        <TabsTrigger
          value="video"
          className={`flex items-center data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white ${
            isSmallMobile ? 'gap-1 text-xs px-1' :  // 优化：超小屏幕减少间距、字体和内边距
            isMobile ? 'gap-1.5 text-sm px-2' : 'gap-2 text-base px-3'
          }`}
        >
          <Video className={`${
            isSmallMobile ? 'w-3 h-3' :  // 优化：超小屏幕减少图标大小
            isMobile ? 'w-3.5 h-3.5' : 'w-4 h-4'
          }`} />
          {isSmallMobile ? t("video").slice(0, 3) : t("video")}  {/* 优化：超小屏幕简化文字 */}
        </TabsTrigger>
      </TabsList>
    </Tabs>
  );
}
