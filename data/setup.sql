-- ========================================
-- 完整数据库初始化脚本
-- 适用于新项目快速启动
-- ========================================

-- 基础用户和系统表
-- ========================================

CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    nickname VARCHAR(255),
    avatar_url VARCHAR(255),
    locale VARCHAR(50) DEFAULT 'en',
    signin_type VARCHAR(50),
    signin_ip VARCHAR(255),
    signin_provider VARCHAR(50),
    signin_openid VARCHAR(255),
    invite_code VARCHAR(255) NOT NULL DEFAULT '',
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    invited_by VARCHAR(255) NOT NULL DEFAULT '',
    is_affiliate BOOLEAN NOT NULL DEFAULT false,
    UNIQUE (email, signin_provider)
);

CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    order_no VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    user_uuid VARCHAR(255) NOT NULL DEFAULT '',
    user_email VARCHAR(255) NOT NULL DEFAULT '',
    amount INT NOT NULL,
    interval VARCHAR(50),
    expired_at TIMESTAMPTZ,
    status VARCHAR(50) NOT NULL,
    stripe_session_id VARCHAR(255),
    credits INT NOT NULL,
    currency VARCHAR(50) DEFAULT 'USD',
    sub_id VARCHAR(255),
    sub_interval_count INT,
    sub_cycle_anchor INT,
    sub_period_end INT,
    sub_period_start INT,
    sub_times INT,
    product_id VARCHAR(255),
    product_name VARCHAR(255),
    valid_months INT,
    order_detail TEXT,
    paid_at TIMESTAMPTZ,
    paid_email VARCHAR(255),
    paid_detail TEXT
);

CREATE TABLE apikeys (
    id SERIAL PRIMARY KEY,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    title VARCHAR(100),
    user_uuid VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'active'
);

CREATE TABLE credits (
    id SERIAL PRIMARY KEY,
    trans_no VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    user_uuid VARCHAR(255) NOT NULL,
    trans_type VARCHAR(50) NOT NULL,
    credits INT NOT NULL,
    order_no VARCHAR(255),
    expired_at TIMESTAMPTZ,
    model_id VARCHAR(100),
    usage_id INT,
    request_id VARCHAR(255)
);

CREATE TABLE posts (
    id SERIAL PRIMARY KEY,
    uuid VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255),
    title VARCHAR(255),
    description TEXT,
    content TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'draft',
    cover_url VARCHAR(255),
    author_name VARCHAR(255),
    author_avatar_url VARCHAR(255),
    locale VARCHAR(50) DEFAULT 'en'
);

CREATE TABLE affiliates (
    id SERIAL PRIMARY KEY,
    user_uuid VARCHAR(255) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    invited_by VARCHAR(255) NOT NULL,
    paid_order_no VARCHAR(255) NOT NULL DEFAULT '',
    paid_amount INT NOT NULL DEFAULT 0,
    reward_percent INT NOT NULL DEFAULT 10,
    reward_amount INT NOT NULL DEFAULT 0
);

CREATE TABLE feedbacks (
    id SERIAL PRIMARY KEY,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    status VARCHAR(50) DEFAULT 'pending',
    user_uuid VARCHAR(255),
    content TEXT,
    rating INT CHECK (rating >= 1 AND rating <= 5)
);

-- AI 模型管理表
-- ========================================

CREATE TABLE ai_models (
    id SERIAL PRIMARY KEY,
    model_id VARCHAR(100) UNIQUE NOT NULL,
    model_name VARCHAR(200) NOT NULL,
    model_type VARCHAR(50) NOT NULL, -- text, image, video, multimodal
    provider VARCHAR(50) NOT NULL, -- grsai, openai, anthropic, replicate
    api_endpoint VARCHAR(200) NOT NULL,
    credits_per_unit INT NOT NULL, -- 每单位消耗的积分
    unit_type VARCHAR(50) NOT NULL, -- tokens, images, videos
    is_active BOOLEAN NOT NULL DEFAULT true,
    description TEXT, -- 保留兼容性
    description_i18n JSONB, -- 多语言描述
    model_name_i18n JSONB, -- 多语言模型名称
    max_input_size INT,
    supported_features JSONB, -- 支持的功能列表
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TABLE ai_model_usage (
    id SERIAL PRIMARY KEY,
    user_uuid VARCHAR(255) NOT NULL,
    model_id VARCHAR(100) NOT NULL,
    request_id VARCHAR(255) UNIQUE NOT NULL,
    input_size INT, -- 输入大小（tokens/pixels等）
    output_size INT, -- 输出大小
    credits_consumed INT NOT NULL, -- 实际消耗的积分
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, success, failed, cancelled
    error_reason VARCHAR(100), -- 错误原因
    error_detail TEXT, -- 详细错误信息
    request_params JSONB, -- 请求参数
    response_data JSONB, -- 响应数据
    started_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建索引
-- ========================================

-- 基础表索引
CREATE INDEX idx_users_uuid ON users(uuid);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_orders_user_uuid ON orders(user_uuid);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created_at ON orders(created_at);
CREATE INDEX idx_apikeys_user_uuid ON apikeys(user_uuid);
CREATE INDEX idx_credits_user_uuid ON credits(user_uuid);
CREATE INDEX idx_credits_model_id ON credits(model_id);
CREATE INDEX idx_credits_request_id ON credits(request_id);
CREATE INDEX idx_posts_status ON posts(status);
CREATE INDEX idx_posts_locale ON posts(locale);

-- AI模型相关索引
CREATE INDEX idx_ai_models_type ON ai_models(model_type);
CREATE INDEX idx_ai_models_provider ON ai_models(provider);
CREATE INDEX idx_ai_models_active ON ai_models(is_active);
CREATE INDEX idx_ai_models_description_i18n ON ai_models USING GIN (description_i18n);
CREATE INDEX idx_ai_models_model_name_i18n ON ai_models USING GIN (model_name_i18n);

CREATE INDEX idx_ai_model_usage_user ON ai_model_usage(user_uuid);
CREATE INDEX idx_ai_model_usage_model ON ai_model_usage(model_id);
CREATE INDEX idx_ai_model_usage_status ON ai_model_usage(status);
CREATE INDEX idx_ai_model_usage_created ON ai_model_usage(created_at);

-- 外键约束
-- ========================================

ALTER TABLE ai_model_usage
ADD CONSTRAINT fk_ai_model_usage_model
FOREIGN KEY (model_id) REFERENCES ai_models(model_id);

-- 实用函数
-- ========================================

-- 获取本地化内容的函数
CREATE OR REPLACE FUNCTION get_localized_content(
  content JSONB,
  locale VARCHAR(5) DEFAULT 'en',
  fallback VARCHAR(5) DEFAULT 'zh'
) RETURNS TEXT AS $$
BEGIN
  IF content IS NULL THEN
    RETURN '';
  END IF;

  IF content ? locale THEN
    RETURN content ->> locale;
  END IF;

  IF content ? fallback THEN
    RETURN content ->> fallback;
  END IF;

  RETURN (SELECT value FROM jsonb_each_text(content) LIMIT 1);
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- 计算模型使用成本的函数
CREATE OR REPLACE FUNCTION calculate_model_cost(
    p_model_id VARCHAR(100),
    p_input_size INT,
    p_output_size INT DEFAULT NULL
) RETURNS INT AS $$
DECLARE
    model_config RECORD;
    total_cost INT;
BEGIN
    SELECT credits_per_unit, unit_type INTO model_config
    FROM ai_models
    WHERE model_id = p_model_id AND is_active = true;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Model % not found or inactive', p_model_id;
    END IF;

    CASE model_config.unit_type
        WHEN 'tokens' THEN
            total_cost := CEIL((COALESCE(p_input_size, 0) + COALESCE(p_output_size, 0)) / 1000.0) * model_config.credits_per_unit;
        WHEN 'images' THEN
            total_cost := model_config.credits_per_unit;
        WHEN 'videos' THEN
            total_cost := model_config.credits_per_unit;
        ELSE
            total_cost := model_config.credits_per_unit;
    END CASE;

    RETURN GREATEST(total_cost, 1);
END;
$$ LANGUAGE plpgsql;

-- 自动更新 updated_at 的触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为相关表创建触发器
CREATE TRIGGER update_ai_models_updated_at
    BEFORE UPDATE ON ai_models
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_posts_updated_at
    BEFORE UPDATE ON posts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- AI模型初始数据
-- ========================================

INSERT INTO ai_models (
  model_id, model_name, model_type, provider, api_endpoint, credits_per_unit, unit_type,
  description, description_i18n, model_name_i18n, max_input_size, supported_features, is_active
) VALUES
-- 文本生成模型
('gemini-2.5-pro', 'Gemini 2.5 Pro', 'text', 'grsai', '/api/ai/generate', 10, 'tokens',
 '高级对话模型，适合复杂任务和专业用途',
 '{"en": "Advanced conversational model for complex tasks and professional use", "zh": "高级对话模型，适合复杂任务和专业用途", "ja": "複雑なタスクと専門用途に適した高度な対話モデル"}',
 '{"en": "Gemini 2.5 Pro", "zh": "Gemini 2.5 专业版", "ja": "Gemini 2.5 プロ"}',
 128000, '["text_generation", "conversation", "analysis"]', true),

('gemini-2.5-flash', 'Gemini 2.5 Flash', 'text', 'grsai', '/api/ai/generate', 5, 'tokens',
 '快速对话模型，响应迅速，效率高',
 '{"en": "Fast conversational model with quick responses and high efficiency", "zh": "快速对话模型，响应迅速，效率高", "ja": "迅速な応答と高効率を持つ高速対話モデル"}',
 '{"en": "Gemini 2.5 Flash", "zh": "Gemini 2.5 闪电版", "ja": "Gemini 2.5 フラッシュ"}',
 128000, '["text_generation", "conversation", "fast_response"]', true),

('gemini-2.5-flash-lite', 'Gemini 2.5 Flash Lite', 'text', 'grsai', '/api/ai/generate', 2, 'tokens',
 '轻量级对话模型，成本低廉，功能基础',
 '{"en": "Lightweight conversational model with low cost and basic features", "zh": "轻量级对话模型，成本低廉，功能基础", "ja": "低コストで基本機能を持つ軽量対話モデル"}',
 '{"en": "Gemini 2.5 Flash Lite", "zh": "Gemini 2.5 轻量版", "ja": "Gemini 2.5 ライト"}',
 64000, '["text_generation", "basic_conversation"]', true),

('gpt-4o-mini', 'GPT-4o Mini', 'text', 'grsai', '/api/ai/generate', 8, 'tokens',
 'GPT-4o 轻量版本，性能与成本平衡',
 '{"en": "Compact version of GPT-4o with balanced performance and cost", "zh": "GPT-4o 轻量版本，性能与成本平衡", "ja": "パフォーマンスとコストのバランスが取れたGPT-4oコンパクト版"}',
 '{"en": "GPT-4o Mini", "zh": "GPT-4o 迷你版", "ja": "GPT-4o ミニ"}',
 128000, '["text_generation", "conversation", "reasoning"]', true),

-- 多模态模型
('o4-mini-all', 'GPT-4o Mini All', 'multimodal', 'grsai', '/api/ai/generate', 12, 'tokens',
 'GPT-4o Mini 全功能版本，支持视觉等多模态能力',
 '{"en": "GPT-4o Mini with full multimodal capabilities including vision", "zh": "GPT-4o Mini 全功能版本，支持视觉等多模态能力", "ja": "ビジョンを含むマルチモーダル機能を持つGPT-4o Mini完全版"}',
 '{"en": "GPT-4o Mini All", "zh": "GPT-4o 迷你全功能版", "ja": "GPT-4o ミニ オール"}',
 128000, '["text_generation", "vision", "multimodal", "reasoning"]', true),

('gpt-4o-all', 'GPT-4o All', 'multimodal', 'grsai', '/api/ai/generate', 20, 'tokens',
 'GPT-4o 完整版本，具备所有高级功能和多模态支持',
 '{"en": "Complete GPT-4o with all advanced features and multimodal support", "zh": "GPT-4o 完整版本，具备所有高级功能和多模态支持", "ja": "すべての高度機能とマルチモーダルサポートを持つ完全なGPT-4o"}',
 '{"en": "GPT-4o All", "zh": "GPT-4o 全功能版", "ja": "GPT-4o オール"}',
 128000, '["text_generation", "vision", "multimodal", "advanced_reasoning", "code_generation"]', true),

-- 图像生成模型
('sora-image', 'Sora Image', 'image', 'grsai', '/api/ai/generate', 50, 'images',
 '基于 Sora 技术的先进图像生成模型',
 '{"en": "Advanced image generation model powered by Sora technology", "zh": "基于 Sora 技术的先进图像生成模型", "ja": "Sora技術による先進的な画像生成モデル"}',
 '{"en": "Sora Image", "zh": "Sora 图像生成", "ja": "Sora 画像生成"}',
 4000, '["image_generation", "high_quality", "creative"]', true),

('gpt-4o-image', 'GPT-4o Image', 'image', 'grsai', '/api/ai/generate', 40, 'images',
 '使用 GPT-4o 架构的高质量图像生成',
 '{"en": "High-quality image generation using GPT-4o architecture", "zh": "使用 GPT-4o 架构的高质量图像生成", "ja": "GPT-4oアーキテクチャを使用した高品質画像生成"}',
 '{"en": "GPT-4o Image", "zh": "GPT-4o 图像生成", "ja": "GPT-4o 画像生成"}',
 4000, '["image_generation", "gpt_powered", "versatile"]', true),

-- Flux 系列
('flux-pro-1.1', 'Flux Pro 1.1', 'image', 'grsai', '/api/ai/generate', 30, 'images',
 'Flux 技术 v1.1 专业图像生成',
 '{"en": "Professional image generation with Flux technology v1.1", "zh": "Flux 技术 v1.1 专业图像生成", "ja": "Flux技術v1.1によるプロフェッショナル画像生成"}',
 '{"en": "Flux Pro 1.1", "zh": "Flux 专业版 1.1", "ja": "Flux プロ 1.1"}',
 4000, '["image_generation", "professional", "flux_tech"]', true),

('flux-pro-1.1-ultra', 'Flux Pro 1.1 Ultra', 'image', 'grsai', '/api/ai/generate', 60, 'images',
 '增强版 Flux Pro 超高质量图像生成',
 '{"en": "Ultra-high quality image generation with enhanced Flux Pro", "zh": "增强版 Flux Pro 超高质量图像生成", "ja": "強化されたFlux Proによる超高品質画像生成"}',
 '{"en": "Flux Pro 1.1 Ultra", "zh": "Flux 超级版 1.1", "ja": "Flux ウルトラ 1.1"}',
 4000, '["image_generation", "ultra_quality", "enhanced_flux"]', true),

('flux-kontext-pro', 'Flux Kontext Pro', 'image', 'grsai', '/api/ai/generate', 45, 'images',
 '上下文感知的专业级图像生成',
 '{"en": "Context-aware image generation with professional quality", "zh": "上下文感知的专业级图像生成", "ja": "コンテキスト認識によるプロフェッショナル品質画像生成"}',
 '{"en": "Flux Kontext Pro", "zh": "Flux 上下文专业版", "ja": "Flux コンテキスト プロ"}',
 4000, '["image_generation", "context_aware", "professional"]', true),

('flux-kontext-max', 'Flux Kontext Max', 'image', 'grsai', '/api/ai/generate', 80, 'images',
 '最高质量的上下文感知图像生成',
 '{"en": "Maximum quality context-aware image generation", "zh": "最高质量的上下文感知图像生成", "ja": "最高品質のコンテキスト認識画像生成"}',
 '{"en": "Flux Kontext Max", "zh": "Flux 上下文最大版", "ja": "Flux コンテキスト マックス"}',
 4000, '["image_generation", "max_quality", "context_aware"]', true),

-- 视频生成模型
('veo3-fast', 'Veo3 Fast', 'video', 'grsai', '/api/ai/generate', 100, 'videos',
 'Veo3 技术快速视频生成，结果迅速',
 '{"en": "Fast video generation with Veo3 technology for quick results", "zh": "Veo3 技术快速视频生成，结果迅速", "ja": "迅速な結果を得るVeo3技術による高速動画生成"}',
 '{"en": "Veo3 Fast", "zh": "Veo3 快速版", "ja": "Veo3 ファスト"}',
 2000, '["video_generation", "fast", "veo3_tech"]', true),

('veo3-pro', 'Veo3 Pro', 'video', 'grsai', '/api/ai/generate', 200, 'videos',
 '具备高级 Veo3 能力的专业视频生成',
 '{"en": "Professional video generation with advanced Veo3 capabilities", "zh": "具备高级 Veo3 能力的专业视频生成", "ja": "高度なVeo3機能を持つプロフェッショナル動画生成"}',
 '{"en": "Veo3 Pro", "zh": "Veo3 专业版", "ja": "Veo3 プロ"}',
 2000, '["video_generation", "professional", "advanced_veo3"]', true);

-- 为所有模型添加其他语言的基础翻译
UPDATE ai_models
SET
  model_name_i18n = model_name_i18n || jsonb_build_object(
    'ko', model_name_i18n->>'en',
    'fr', model_name_i18n->>'en',
    'de', model_name_i18n->>'en',
    'es', model_name_i18n->>'en',
    'it', model_name_i18n->>'en',
    'pt', model_name_i18n->>'en',
    'ru', model_name_i18n->>'en'
  ),
  description_i18n = description_i18n || jsonb_build_object(
    'ko', description_i18n->>'en',
    'fr', description_i18n->>'en',
    'de', description_i18n->>'en',
    'es', description_i18n->>'en',
    'it', description_i18n->>'en',
    'pt', description_i18n->>'en',
    'ru', description_i18n->>'en'
  )
WHERE model_name_i18n IS NOT NULL AND description_i18n IS NOT NULL;

-- 实用视图
-- ========================================

-- 用户积分使用统计视图
CREATE OR REPLACE VIEW user_credits_usage_stats AS
SELECT
    u.user_uuid,
    u.model_id,
    am.model_name,
    am.model_type,
    COUNT(*) as usage_count,
    SUM(u.credits_consumed) as total_credits_consumed,
    AVG(u.credits_consumed) as avg_credits_per_use,
    COUNT(CASE WHEN u.status = 'success' THEN 1 END) as success_count,
    COUNT(CASE WHEN u.status = 'failed' THEN 1 END) as failed_count,
    MAX(u.created_at) as last_used_at
FROM ai_model_usage u
JOIN ai_models am ON u.model_id = am.model_id
GROUP BY u.user_uuid, u.model_id, am.model_name, am.model_type;

-- 模型使用统计视图
CREATE OR REPLACE VIEW model_usage_stats AS
SELECT
    am.model_id,
    am.model_name,
    am.model_type,
    am.provider,
    COUNT(u.id) as total_usage_count,
    COUNT(DISTINCT u.user_uuid) as unique_users,
    SUM(u.credits_consumed) as total_credits_consumed,
    AVG(u.credits_consumed) as avg_credits_per_use,
    COUNT(CASE WHEN u.status = 'success' THEN 1 END) as success_count,
    COUNT(CASE WHEN u.status = 'failed' THEN 1 END) as failed_count,
    ROUND(COUNT(CASE WHEN u.status = 'success' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0), 2) as success_rate
FROM ai_models am
LEFT JOIN ai_model_usage u ON am.model_id = u.model_id
WHERE am.is_active = true
GROUP BY am.model_id, am.model_name, am.model_type, am.provider
ORDER BY total_usage_count DESC;

-- 多语言模型视图
CREATE OR REPLACE VIEW ai_models_localized AS
SELECT
  id,
  model_id,
  get_localized_content(model_name_i18n, 'en', 'zh') as model_name_en,
  get_localized_content(model_name_i18n, 'zh', 'en') as model_name_zh,
  get_localized_content(model_name_i18n, 'ja', 'en') as model_name_ja,
  model_type,
  provider,
  api_endpoint,
  credits_per_unit,
  unit_type,
  is_active,
  get_localized_content(description_i18n, 'en', 'zh') as description_en,
  get_localized_content(description_i18n, 'zh', 'en') as description_zh,
  get_localized_content(description_i18n, 'ja', 'en') as description_ja,
  description_i18n,
  model_name_i18n,
  max_input_size,
  supported_features,
  created_at,
  updated_at
FROM ai_models;

-- 字段注释
-- ========================================

COMMENT ON COLUMN ai_models.description IS 'DEPRECATED: Use description_i18n instead';
COMMENT ON COLUMN ai_models.model_name IS 'DEPRECATED: Use model_name_i18n instead';
COMMENT ON COLUMN ai_models.description_i18n IS 'Multilingual description in JSON format: {"en": "English", "zh": "中文"}';
COMMENT ON COLUMN ai_models.model_name_i18n IS 'Multilingual model name in JSON format: {"en": "English", "zh": "中文"}';

-- 初始化完成
-- ========================================
-- 数据库初始化完成！
-- 包含：14个AI模型（文本/图像/视频/多模态）
-- 支持：多语言、积分系统、用户管理、订单系统
-- 新项目可以直接使用此脚本快速启动
