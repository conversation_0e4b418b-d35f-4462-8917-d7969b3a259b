import { Section } from "@/types/blocks/section";

/**
 * Prompt Showcase 组件的数据项接口
 */
export interface PromptShowcaseItem {
  /** 标题 */
  title: string;
  /** 描述 */
  description?: string;
  /** Prompt 内容 */
  prompt: string;
  /** 主要展示图片（单张图片） */
  image?: {
    src: string;
    alt?: string;
  };
  /** 多张图片（用于卡片内轮播） */
  images?: {
    src: string;
    alt?: string;
  }[];
  /** 分类标签 */
  category?: string;
  /** 标签列表 */
  tags?: string[];
}

/**
 * Comparison Showcase 组件的数据项接口
 */
export interface ComparisonShowcaseItem {
  /** 标题 */
  title: string;
  /** 描述 */
  description?: string;
  /** Prompt 内容 */
  prompt: string;
  /** 处理前图片 */
  beforeImage: {
    src: string;
    alt: string;
  };
  /** 处理后图片 */
  afterImage: {
    src: string;
    alt: string;
  };
  /** 分类标签 */
  category?: string;
  /** 标签列表 */
  tags?: string[];
}

/**
 * 布局类型
 */
export type LayoutType = 'horizontal' | 'vertical';

/**
 * 水平布局时的图片位置
 */
export type ImagePosition = 'left' | 'right';

/**
 * 卡片宽度类型
 */
export type CardWidth = '50%' | '100%' | 'auto';

/**
 * Prompt Showcase 组件的 Props 接口
 */
export interface PromptShowcaseProps {
  section: Omit<Section, 'items'> & {
    items?: PromptShowcaseItem[];
  };
  /** 是否启用卡片轮播 */
  enableCarousel?: boolean;
  /** 布局类型：水平或垂直 */
  layout?: LayoutType;
  /** 水平布局时图片位置 */
  imagePosition?: ImagePosition;
  /** 卡片宽度配置 */
  cardWidth?: CardWidth;
  /** 卡片轮播自动播放间隔（毫秒），0表示不自动播放 */
  autoplayDelay?: number;
}

/**
 * Comparison Showcase 组件的 Props 接口
 */
export interface ComparisonShowcaseProps {
  section: Omit<Section, 'items'> & {
    items?: ComparisonShowcaseItem[];
  };
  /** 是否启用卡片轮播 */
  enableCarousel?: boolean;
  /** 卡片宽度配置 */
  cardWidth?: CardWidth;
  /** 卡片轮播自动播放间隔（毫秒），0表示不自动播放 */
  autoplayDelay?: number;
}
