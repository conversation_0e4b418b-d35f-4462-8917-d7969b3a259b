import {
  findCreditByOrderNo,
  getUserValidCredits,
  insertCredit,
} from "@/models/credit";

import { Credit } from "@/types/credit";
import { Order } from "@/types/order";
import { UserCredits } from "@/types/user";
import { findUserByUuid } from "@/models/user";
import { getFirstPaidOrderByUserUuid } from "@/models/order";
import { getIsoTimestr, getOneYearLaterTimestr } from "@/lib/time";
import { getSnowId } from "@/lib/hash";

export enum CreditsTransType {
  NewUser = "new_user", // initial credits for new user
  OrderPay = "order_pay", // user pay for credits
  SystemAdd = "system_add", // system add credits
  Ping = "ping", // cost for ping api
  TextGeneration = "text_generation", // AI text generation
  ImageGeneration = "image_generation", // AI image generation
  VideoGeneration = "video_generation", // AI video generation
  AIModelUsage = "ai_model_usage", // general AI model usage
}

export enum CreditsAmount {
  NewUserGet = 100,
  PingCost = 1,
  // AI模型基础成本（将根据具体模型动态计算）
  TextGenerationBase = 5,
  ImageGenerationBase = 50,
  VideoGenerationBase = 200,
}

export async function getUserCredits(user_uuid: string): Promise<UserCredits> {
  let user_credits: UserCredits = {
    left_credits: 0,
  };

  try {
    const first_paid_order = await getFirstPaidOrderByUserUuid(user_uuid);
    if (first_paid_order) {
      user_credits.is_recharged = true;
    }

    const credits = await getUserValidCredits(user_uuid);
    if (credits) {
      credits.forEach((v: Credit) => {
        user_credits.left_credits += v.credits;
      });
    }

    if (user_credits.left_credits < 0) {
      user_credits.left_credits = 0;
    }

    if (user_credits.left_credits > 0) {
      user_credits.is_pro = true;
    }

    return user_credits;
  } catch (e) {
    console.log("get user credits failed: ", e);
    return user_credits;
  }
}

export async function decreaseCredits({
  user_uuid,
  trans_type,
  credits,
}: {
  user_uuid: string;
  trans_type: CreditsTransType;
  credits: number;
}) {
  try {
    let order_no = "";
    let expired_at = "";
    let left_credits = 0;

    const userCredits = await getUserValidCredits(user_uuid);
    if (userCredits) {
      for (let i = 0, l = userCredits.length; i < l; i++) {
        const credit = userCredits[i];
        left_credits += credit.credits;

        // credit enough for cost
        if (left_credits >= credits) {
          order_no = credit.order_no;
          expired_at = credit.expired_at || "";
          break;
        }

        // look for next credit
      }
    }

    const new_credit: Credit = {
      trans_no: getSnowId(),
      created_at: getIsoTimestr(),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: 0 - credits,
      order_no: order_no,
      expired_at: expired_at,
    };
    await insertCredit(new_credit);
  } catch (e) {
    console.log("decrease credits failed: ", e);
    throw e;
  }
}

export async function increaseCredits({
  user_uuid,
  trans_type,
  credits,
  expired_at,
  order_no,
}: {
  user_uuid: string;
  trans_type: string;
  credits: number;
  expired_at?: string;
  order_no?: string;
}) {
  try {
    const new_credit: Credit = {
      trans_no: getSnowId(),
      created_at: getIsoTimestr(),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: credits,
      order_no: order_no || "",
      expired_at: expired_at || "",
    };
    await insertCredit(new_credit);
  } catch (e) {
    console.log("increase credits failed: ", e);
    throw e;
  }
}

export async function updateCreditForOrder(order: Order) {
  try {
    const credit = await findCreditByOrderNo(order.order_no);
    if (credit) {
      // order already increased credit
      return;
    }

    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: order.expired_at,
      order_no: order.order_no,
    });
  } catch (e) {
    console.log("update credit for order failed: ", e);
    throw e;
  }
}

/**
 * 为AI模型使用扣减积分
 */
export async function decreaseCreditsForAIModel({
  user_uuid,
  model_id,
  request_id,
  credits,
  trans_type = CreditsTransType.AIModelUsage,
}: {
  user_uuid: string;
  model_id: string;
  request_id: string;
  credits: number;
  trans_type?: CreditsTransType;
}) {
  try {
    let order_no = "";
    let expired_at = "";
    let left_credits = 0;

    const userCredits = await getUserValidCredits(user_uuid);
    if (userCredits) {
      for (let i = 0, l = userCredits.length; i < l; i++) {
        const credit = userCredits[i];
        left_credits += credit.credits;

        // credit enough for cost
        if (left_credits >= credits) {
          order_no = credit.order_no;
          expired_at = credit.expired_at || "";
          break;
        }
      }
    }

    // 检查积分是否足够
    if (left_credits < credits) {
      throw new Error("Insufficient credits");
    }

    const new_credit: Credit = {
      trans_no: getSnowId(),
      created_at: getIsoTimestr(),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: 0 - credits,
      order_no: order_no,
      expired_at: expired_at,
      model_id: model_id,
      request_id: request_id,
    };
    await insertCredit(new_credit);
  } catch (e) {
    console.log("decrease credits for AI model failed: ", e);
    throw e;
  }
}

/**
 * 退还AI模型使用的积分（当生成失败且错误类型为"error"时）
 */
export async function refundCreditsForAIModel({
  user_uuid,
  model_id,
  request_id,
  credits,
  original_trans_no,
}: {
  user_uuid: string;
  model_id: string;
  request_id: string;
  credits: number;
  original_trans_no: string;
}) {
  try {
    const refund_credit: Credit = {
      trans_no: getSnowId(),
      created_at: getIsoTimestr(),
      user_uuid: user_uuid,
      trans_type: "ai_model_refund",
      credits: credits, // 正数，表示退还
      order_no: "",
      expired_at: getOneYearLaterTimestr(), // 退还的积分有效期一年
      model_id: model_id,
      request_id: request_id,
    };
    await insertCredit(refund_credit);
  } catch (e) {
    console.log("refund credits for AI model failed: ", e);
    throw e;
  }
}

/**
 * 检查用户是否有足够的积分
 */
export async function checkSufficientCredits(
  user_uuid: string,
  required_credits: number
): Promise<boolean> {
  try {
    const userCredits = await getUserCredits(user_uuid);
    return userCredits.left_credits >= required_credits;
  } catch (e) {
    console.log("check sufficient credits failed: ", e);
    return false;
  }
}
